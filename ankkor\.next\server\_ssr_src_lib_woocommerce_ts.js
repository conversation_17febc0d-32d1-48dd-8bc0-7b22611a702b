"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_woocommerce_ts";
exports.ids = ["_ssr_src_lib_woocommerce_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/wooInventoryMapping.ts":
/*!****************************************!*\
  !*** ./src/lib/wooInventoryMapping.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addInventoryMapping: () => (/* binding */ addInventoryMapping),\n/* harmony export */   clearInventoryMappings: () => (/* binding */ clearInventoryMappings),\n/* harmony export */   getAllInventoryMappings: () => (/* binding */ getAllInventoryMappings),\n/* harmony export */   getAllShopifyToWooMappings: () => (/* binding */ getAllShopifyToWooMappings),\n/* harmony export */   getInventoryMapping: () => (/* binding */ getInventoryMapping),\n/* harmony export */   getProductSlugFromInventory: () => (/* binding */ getProductSlugFromInventory),\n/* harmony export */   getWooIdFromShopifyId: () => (/* binding */ getWooIdFromShopifyId),\n/* harmony export */   initializeFromProducts: () => (/* binding */ initializeFromProducts),\n/* harmony export */   loadInventoryMap: () => (/* binding */ loadInventoryMap),\n/* harmony export */   mapShopifyToWooId: () => (/* binding */ mapShopifyToWooId),\n/* harmony export */   saveInventoryMap: () => (/* binding */ saveInventoryMap),\n/* harmony export */   updateInventoryMapping: () => (/* binding */ updateInventoryMapping),\n/* harmony export */   updateInventoryMappings: () => (/* binding */ updateInventoryMappings),\n/* harmony export */   validateProductId: () => (/* binding */ validateProductId)\n/* harmony export */ });\n/* harmony import */ var _upstash_redis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @upstash/redis */ \"(ssr)/./node_modules/@upstash/redis/nodejs.mjs\");\n\n// Redis key prefix for inventory mappings\nconst KEY_PREFIX = \"woo:inventory:mapping:\";\n// Redis key for the mapping between Shopify and WooCommerce IDs\nconst SHOPIFY_TO_WOO_KEY = \"shopify:to:woo:mapping\";\n// Initialize Redis client with support for both Upstash Redis and Vercel KV variables\nconst redis = new _upstash_redis__WEBPACK_IMPORTED_MODULE_0__.Redis({\n    url: process.env.UPSTASH_REDIS_REST_URL || process.env.NEXT_PUBLIC_KV_REST_API_URL || \"\",\n    token: process.env.UPSTASH_REDIS_REST_TOKEN || process.env.NEXT_PUBLIC_KV_REST_API_TOKEN || \"\"\n});\n// In-memory fallback for local development or when Redis is unavailable\nconst memoryStorage = {};\nconst shopifyToWooMemoryStorage = {};\n// Key for storing inventory mapping in KV store\nconst INVENTORY_MAPPING_KEY = \"woo-inventory-mapping\";\n/**\n * Check if Redis is available\n */ function isRedisAvailable() {\n    return Boolean(process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN || process.env.NEXT_PUBLIC_KV_REST_API_URL && process.env.NEXT_PUBLIC_KV_REST_API_TOKEN);\n}\n/**\n * Load inventory mapping from storage\n * Maps WooCommerce product IDs to product slugs\n * \n * @returns A record mapping product IDs to product slugs\n */ async function loadInventoryMap() {\n    // Use Redis if available\n    if (isRedisAvailable()) {\n        try {\n            // Get all keys with our prefix\n            const keys = await redis.keys(`${KEY_PREFIX}*`);\n            if (keys.length === 0) {\n                console.log(\"No existing WooCommerce inventory mappings found in Redis\");\n                return {};\n            }\n            // Create a mapping object\n            const map = {};\n            // Get all values in a single batch operation\n            const values = await redis.mget(...keys);\n            // Populate the mapping object\n            keys.forEach((key, index)=>{\n                const productId = key.replace(KEY_PREFIX, \"\");\n                const productSlug = values[index];\n                map[productId] = productSlug;\n            });\n            console.log(`Loaded WooCommerce inventory mapping with ${Object.keys(map).length} entries from Redis`);\n            return map;\n        } catch (error) {\n            console.error(\"Error loading WooCommerce inventory mapping from Redis:\", error);\n            console.log(\"Falling back to in-memory storage\");\n            return {\n                ...memoryStorage\n            };\n        }\n    } else {\n        // Fallback to in-memory when Redis is not available\n        return {\n            ...memoryStorage\n        };\n    }\n}\n/**\n * Save inventory mapping to storage\n * \n * @param map The inventory mapping to save\n */ async function saveInventoryMap(map) {\n    // Use Redis if available\n    if (isRedisAvailable()) {\n        try {\n            // Convert map to array of Redis commands\n            const pipeline = redis.pipeline();\n            // First clear existing keys with this prefix\n            const existingKeys = await redis.keys(`${KEY_PREFIX}*`);\n            if (existingKeys.length > 0) {\n                pipeline.del(...existingKeys);\n            }\n            // Set new key-value pairs\n            Object.entries(map).forEach(([productId, productSlug])=>{\n                pipeline.set(`${KEY_PREFIX}${productId}`, productSlug);\n            });\n            // Execute all commands in a single transaction\n            await pipeline.exec();\n            console.log(`Saved WooCommerce inventory mapping with ${Object.keys(map).length} entries to Redis`);\n        } catch (error) {\n            console.error(\"Error saving WooCommerce inventory mapping to Redis:\", error);\n            console.log(\"Falling back to in-memory storage\");\n            // Update in-memory storage as fallback\n            Object.assign(memoryStorage, map);\n        }\n    } else {\n        // Fallback to in-memory when Redis is not available\n        Object.assign(memoryStorage, map);\n        console.log(`Saved WooCommerce inventory mapping with ${Object.keys(map).length} entries to memory`);\n    }\n}\n/**\n * Add a mapping between a WooCommerce product ID and a product slug\n * \n * @param productId The WooCommerce product ID\n * @param productSlug The product slug\n * @returns True if the mapping was added or updated, false if there was an error\n */ async function addInventoryMapping(productId, productSlug) {\n    try {\n        if (isRedisAvailable()) {\n            await redis.set(`${KEY_PREFIX}${productId}`, productSlug);\n            console.log(`Added WooCommerce mapping to Redis: ${productId} -> ${productSlug}`);\n        } else {\n            memoryStorage[productId] = productSlug;\n            console.log(`Added WooCommerce mapping to memory: ${productId} -> ${productSlug}`);\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Error adding WooCommerce inventory mapping:\", error);\n        // Try memory as fallback\n        try {\n            memoryStorage[productId] = productSlug;\n            console.log(`Added WooCommerce mapping to memory fallback: ${productId} -> ${productSlug}`);\n            return true;\n        } catch (memError) {\n            console.error(\"Error adding to memory fallback:\", memError);\n            return false;\n        }\n    }\n}\n/**\n * Get the product slug associated with a WooCommerce product ID\n * \n * @param productId The WooCommerce product ID\n * @returns The product slug, or null if not found\n */ async function getProductSlugFromInventory(productId) {\n    try {\n        if (isRedisAvailable()) {\n            const slug = await redis.get(`${KEY_PREFIX}${productId}`);\n            return slug || null;\n        } else {\n            return memoryStorage[productId] || null;\n        }\n    } catch (error) {\n        console.error(\"Error getting product slug from Redis:\", error);\n        // Try memory as fallback\n        try {\n            return memoryStorage[productId] || null;\n        } catch (memError) {\n            console.error(\"Error getting from memory fallback:\", memError);\n            return null;\n        }\n    }\n}\n/**\n * Batch update multiple WooCommerce inventory mappings\n * \n * @param mappings An array of product ID to product slug mappings\n * @returns True if all mappings were successfully updated, false otherwise\n */ async function updateInventoryMappings(mappings) {\n    try {\n        if (isRedisAvailable()) {\n            const pipeline = redis.pipeline();\n            for (const { productId, productSlug } of mappings){\n                pipeline.set(`${KEY_PREFIX}${productId}`, productSlug);\n            }\n            await pipeline.exec();\n            console.log(`Updated ${mappings.length} WooCommerce inventory mappings in Redis`);\n        } else {\n            for (const { productId, productSlug } of mappings){\n                memoryStorage[productId] = productSlug;\n            }\n            console.log(`Updated ${mappings.length} WooCommerce inventory mappings in memory`);\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Error batch updating WooCommerce inventory mappings:\", error);\n        return false;\n    }\n}\n/**\n * Get all WooCommerce inventory mappings\n * \n * @returns The complete inventory map\n */ async function getAllInventoryMappings() {\n    return await loadInventoryMap();\n}\n/**\n * Clear all WooCommerce inventory mappings\n * \n * @returns True if successfully cleared, false otherwise\n */ async function clearInventoryMappings() {\n    try {\n        if (isRedisAvailable()) {\n            const keys = await redis.keys(`${KEY_PREFIX}*`);\n            if (keys.length > 0) {\n                await redis.del(...keys);\n            }\n            console.log(\"Cleared all WooCommerce inventory mappings from Redis\");\n        }\n        // Clear memory storage regardless of Redis availability\n        Object.keys(memoryStorage).forEach((key)=>{\n            delete memoryStorage[key];\n        });\n        return true;\n    } catch (error) {\n        console.error(\"Error clearing WooCommerce inventory mappings:\", error);\n        return false;\n    }\n}\n/**\n * Map a Shopify product ID to a WooCommerce product ID\n * \n * @param shopifyId The Shopify product ID\n * @param wooId The WooCommerce product ID\n * @returns True if the mapping was added successfully, false otherwise\n */ async function mapShopifyToWooId(shopifyId, wooId) {\n    try {\n        if (isRedisAvailable()) {\n            // Get existing mappings\n            const existingMap = await redis.hgetall(SHOPIFY_TO_WOO_KEY) || {};\n            // Add new mapping\n            existingMap[shopifyId] = wooId;\n            // Save updated mappings\n            await redis.hset(SHOPIFY_TO_WOO_KEY, existingMap);\n            console.log(`Mapped Shopify ID ${shopifyId} to WooCommerce ID ${wooId} in Redis`);\n            return true;\n        } else {\n            // Fallback to in-memory\n            shopifyToWooMemoryStorage[shopifyId] = wooId;\n            console.log(`Mapped Shopify ID ${shopifyId} to WooCommerce ID ${wooId} in memory`);\n            return true;\n        }\n    } catch (error) {\n        console.error(\"Error mapping Shopify ID to WooCommerce ID:\", error);\n        return false;\n    }\n}\n/**\n * Get the WooCommerce ID corresponding to a Shopify ID\n * \n * @param shopifyId The original Shopify product ID or inventory item ID\n * @returns The corresponding WooCommerce ID, or null if not found\n */ async function getWooIdFromShopifyId(shopifyId) {\n    try {\n        if (isRedisAvailable()) {\n            const wooId = await redis.hget(SHOPIFY_TO_WOO_KEY, shopifyId);\n            return wooId || null;\n        } else {\n            return shopifyToWooMemoryStorage[shopifyId] || null;\n        }\n    } catch (error) {\n        console.error(`Error getting WooCommerce ID for Shopify ID ${shopifyId}:`, error);\n        // Try memory as fallback\n        try {\n            return shopifyToWooMemoryStorage[shopifyId] || null;\n        } catch (memError) {\n            console.error(\"Error getting from memory fallback:\", memError);\n            return null;\n        }\n    }\n}\n/**\n * Get all Shopify to WooCommerce ID mappings\n * \n * @returns Record of Shopify IDs to WooCommerce IDs\n */ async function getAllShopifyToWooMappings() {\n    try {\n        if (isRedisAvailable()) {\n            const mappings = await redis.hgetall(SHOPIFY_TO_WOO_KEY);\n            return mappings || {};\n        } else {\n            return {\n                ...shopifyToWooMemoryStorage\n            };\n        }\n    } catch (error) {\n        console.error(\"Error getting all Shopify to WooCommerce mappings:\", error);\n        return {\n            ...shopifyToWooMemoryStorage\n        };\n    }\n}\n/**\n * Initialize inventory mappings from WooCommerce products\n * This function should be called after initial product import or periodically to refresh the mappings\n * \n * @param products Array of WooCommerce products with id and slug properties\n * @returns True if successfully initialized, false otherwise\n */ async function initializeFromProducts(products) {\n    try {\n        const inventoryMappings = [];\n        const idMappings = [];\n        for (const product of products){\n            // Add to inventory mappings\n            inventoryMappings.push({\n                productId: product.id,\n                productSlug: product.slug\n            });\n            // If this product has a Shopify ID, add to ID mappings\n            if (product.shopifyId) {\n                idMappings.push({\n                    shopifyId: product.shopifyId,\n                    wooId: product.id\n                });\n            }\n        }\n        // Update inventory mappings\n        await updateInventoryMappings(inventoryMappings);\n        // Update ID mappings\n        for (const { shopifyId, wooId } of idMappings){\n            await mapShopifyToWooId(shopifyId, wooId);\n        }\n        console.log(`Initialized ${inventoryMappings.length} inventory mappings and ${idMappings.length} ID mappings`);\n        return true;\n    } catch (error) {\n        console.error(\"Error initializing inventory mappings from products:\", error);\n        return false;\n    }\n}\n/**\n * Get the current inventory mapping\n * \n * @returns The inventory mapping\n */ async function getInventoryMapping() {\n    try {\n        // Try to get the mapping from Redis\n        if (isRedisAvailable()) {\n            const allKeys = await redis.keys(`${KEY_PREFIX}*`);\n            if (allKeys.length > 0) {\n                const mapping = {};\n                const values = await redis.mget(...allKeys);\n                allKeys.forEach((key, index)=>{\n                    const productId = key.replace(KEY_PREFIX, \"\");\n                    const slug = values[index];\n                    mapping[productId] = {\n                        wooId: productId,\n                        inventory: 0,\n                        sku: \"\",\n                        title: slug,\n                        lastUpdated: new Date().toISOString()\n                    };\n                });\n                return mapping;\n            }\n        }\n        // Default empty mapping\n        return {};\n    } catch (error) {\n        console.error(\"Error getting inventory mapping:\", error);\n        return {};\n    }\n}\n/**\n * Update the inventory mapping\n * \n * @param mapping The inventory mapping to save\n * @returns True if successful, false otherwise\n */ async function updateInventoryMapping(mapping) {\n    try {\n        if (isRedisAvailable()) {\n            // First clear existing keys\n            const existingKeys = await redis.keys(`${KEY_PREFIX}*`);\n            if (existingKeys.length > 0) {\n                await redis.del(...existingKeys);\n            }\n            // Add each product mapping\n            const pipeline = redis.pipeline();\n            for (const [productId, details] of Object.entries(mapping)){\n                pipeline.set(`${KEY_PREFIX}${productId}`, details.title || productId);\n            }\n            await pipeline.exec();\n            return true;\n        }\n        return false;\n    } catch (error) {\n        console.error(\"Error updating inventory mapping:\", error);\n        return false;\n    }\n}\n/**\n * Validate and transform product ID\n * \n * This function helps with the migration from Shopify to WooCommerce by:\n * 1. Checking if the ID is a valid WooCommerce ID\n * 2. If not, attempting to map from Shopify ID to WooCommerce ID\n * 3. Returning a normalized ID or the original ID if no mapping found\n * \n * @param productId The product ID to validate (could be Shopify or WooCommerce ID)\n * @returns A valid WooCommerce product ID or the original ID if no mapping found\n */ async function validateProductId(productId) {\n    if (!productId || productId === \"undefined\" || productId === \"null\") {\n        console.warn(\"Invalid product ID received:\", productId);\n        return productId; // Return the original ID even if invalid\n    }\n    // Check if this looks like a Shopify ID (gid://shopify/Product/123456789)\n    if (productId.includes(\"gid://shopify/Product/\")) {\n        console.log(`Detected Shopify ID: ${productId}, attempting to map to WooCommerce ID`);\n        // Try to get the WooCommerce ID from our mapping\n        const wooId = await getWooIdFromShopifyId(productId);\n        if (wooId) {\n            console.log(`Mapped Shopify ID ${productId} to WooCommerce ID ${wooId}`);\n            return wooId;\n        } else {\n            console.warn(`No mapping found for Shopify ID: ${productId}, using original ID`);\n            return productId; // Return the original ID if no mapping found\n        }\n    }\n    // If it's a base64 encoded ID like \"cG9zdDo2MA==\", it might be a WooCommerce ID\n    // but we should check if it actually exists in our inventory mapping\n    if (productId.includes(\"=\")) {\n        const slug = await getProductSlugFromInventory(productId);\n        if (slug) {\n            // We have a mapping for this ID, so it's likely valid\n            return productId;\n        } else {\n            console.warn(`Product ID ${productId} not found in inventory mapping, using as is`);\n            // We'll still return the ID and let the GraphQL API handle the validation\n            return productId;\n        }\n    }\n    // If it's a numeric ID, it's likely a valid WooCommerce product ID\n    if (/^\\d+$/.test(productId)) {\n        return productId;\n    }\n    // For any other format, return as is and let the GraphQL API validate\n    return productId;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/wooInventoryMapping.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/woocommerce.ts":
/*!********************************!*\
  !*** ./src/lib/woocommerce.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADD_TO_CART: () => (/* binding */ ADD_TO_CART),\n/* harmony export */   GET_CART: () => (/* binding */ GET_CART),\n/* harmony export */   MUTATION_LOGIN: () => (/* binding */ MUTATION_LOGIN),\n/* harmony export */   MUTATION_REMOVE_FROM_CART: () => (/* binding */ MUTATION_REMOVE_FROM_CART),\n/* harmony export */   QUERY_ALL_CATEGORIES: () => (/* binding */ QUERY_ALL_CATEGORIES),\n/* harmony export */   QUERY_ALL_PRODUCTS: () => (/* binding */ QUERY_ALL_PRODUCTS),\n/* harmony export */   QUERY_CATEGORY_PRODUCTS: () => (/* binding */ QUERY_CATEGORY_PRODUCTS),\n/* harmony export */   QUERY_GET_CART: () => (/* binding */ QUERY_GET_CART),\n/* harmony export */   QUERY_PAYMENT_GATEWAYS: () => (/* binding */ QUERY_PAYMENT_GATEWAYS),\n/* harmony export */   QUERY_SHIPPING_METHODS: () => (/* binding */ QUERY_SHIPPING_METHODS),\n/* harmony export */   addToCart: () => (/* binding */ addToCart),\n/* harmony export */   clearAuthToken: () => (/* binding */ clearAuthToken),\n/* harmony export */   createAddress: () => (/* binding */ createAddress),\n/* harmony export */   createCart: () => (/* binding */ createCart),\n/* harmony export */   createCustomer: () => (/* binding */ createCustomer),\n/* harmony export */   customerLogin: () => (/* binding */ customerLogin),\n/* harmony export */   deleteAddress: () => (/* binding */ deleteAddress),\n/* harmony export */   fetchFromWooCommerce: () => (/* binding */ fetchFromWooCommerce),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getAllCategories: () => (/* binding */ getAllCategories),\n/* harmony export */   getAllProducts: () => (/* binding */ getAllProducts),\n/* harmony export */   getCart: () => (/* binding */ getCart),\n/* harmony export */   getCategories: () => (/* binding */ getCategories),\n/* harmony export */   getCategoryProducts: () => (/* binding */ getCategoryProducts),\n/* harmony export */   getCategoryProductsWithTags: () => (/* binding */ getCategoryProductsWithTags),\n/* harmony export */   getCustomer: () => (/* binding */ getCustomer),\n/* harmony export */   getMetafield: () => (/* binding */ getMetafield),\n/* harmony export */   getProduct: () => (/* binding */ getProduct),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductBySlug: () => (/* binding */ getProductBySlug),\n/* harmony export */   getProductBySlugWithTags: () => (/* binding */ getProductBySlugWithTags),\n/* harmony export */   getProductVariations: () => (/* binding */ getProductVariations),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   getProductsByCategory: () => (/* binding */ getProductsByCategory),\n/* harmony export */   getProductsByTag: () => (/* binding */ getProductsByTag),\n/* harmony export */   getSessionToken: () => (/* binding */ getSessionToken),\n/* harmony export */   getWooCommerceCheckoutUrl: () => (/* binding */ getWooCommerceCheckoutUrl),\n/* harmony export */   normalizeCart: () => (/* binding */ normalizeCart),\n/* harmony export */   normalizeCategory: () => (/* binding */ normalizeCategory),\n/* harmony export */   normalizeProduct: () => (/* binding */ normalizeProduct),\n/* harmony export */   normalizeProductImages: () => (/* binding */ normalizeProductImages),\n/* harmony export */   removeFromCart: () => (/* binding */ removeFromCart),\n/* harmony export */   searchProducts: () => (/* binding */ searchProducts),\n/* harmony export */   setAuthToken: () => (/* binding */ setAuthToken),\n/* harmony export */   setDefaultAddress: () => (/* binding */ setDefaultAddress),\n/* harmony export */   setSessionToken: () => (/* binding */ setSessionToken),\n/* harmony export */   testWooCommerceConnection: () => (/* binding */ testWooCommerceConnection),\n/* harmony export */   updateAddress: () => (/* binding */ updateAddress),\n/* harmony export */   updateCart: () => (/* binding */ updateCart),\n/* harmony export */   updateCustomer: () => (/* binding */ updateCustomer),\n/* harmony export */   wooConfig: () => (/* binding */ wooConfig),\n/* harmony export */   wooGraphQLFetch: () => (/* binding */ wooGraphQLFetch)\n/* harmony export */ });\n/* harmony import */ var graphql_request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! graphql-request */ \"graphql-request\");\n/* harmony import */ var _wooInventoryMapping__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./wooInventoryMapping */ \"(ssr)/./src/lib/wooInventoryMapping.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([graphql_request__WEBPACK_IMPORTED_MODULE_0__]);\ngraphql_request__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// WooCommerce GraphQL API integration - Fixed according to official documentation\n\n\n\n// WooCommerce store configuration\nconst wooConfig = {\n    storeUrl: \"https://maroon-lapwing-781450.hostingersite.com\" || 0,\n    graphqlUrl: process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\",\n    apiVersion: \"v1\"\n};\n// Session management for WooCommerce\nlet sessionToken = null;\nconst getSessionToken = ()=>{\n    if (false) {}\n    return sessionToken;\n};\nconst setSessionToken = (token)=>{\n    sessionToken = token;\n    if (false) {}\n};\n// Check if code is running on client or server\nconst isClient = \"undefined\" !== \"undefined\";\n// Initialize GraphQL client with proper headers for CORS\nconst endpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\";\nconst graphQLClient = new graphql_request__WEBPACK_IMPORTED_MODULE_0__.GraphQLClient(endpoint, {\n    headers: {\n        \"Content-Type\": \"application/json\",\n        \"Accept\": \"application/json\"\n    }\n});\n// Set auth token for authenticated requests\nconst setAuthToken = (token)=>{\n    graphQLClient.setHeader(\"Authorization\", `Bearer ${token}`);\n};\n// Clear auth token for unauthenticated requests\nconst clearAuthToken = ()=>{\n    graphQLClient.setHeaders({\n        \"Content-Type\": \"application/json\",\n        \"Accept\": \"application/json\"\n    });\n};\n// GraphQL fragments\nconst PRODUCT_FRAGMENT = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  fragment ProductFields on Product {\n    id\n    databaseId\n    name\n    slug\n    description\n    shortDescription\n    type\n    image {\n      sourceUrl\n      altText\n    }\n    galleryImages {\n      nodes {\n        sourceUrl\n        altText\n      }\n    }\n    ... on SimpleProduct {\n      price\n      regularPrice\n      salePrice\n      onSale\n      stockStatus\n      stockQuantity\n    }\n    ... on VariableProduct {\n      price\n      regularPrice\n      salePrice\n      onSale\n      stockStatus\n      stockQuantity\n      attributes {\n        nodes {\n          name\n          options\n        }\n      }\n    }\n  }\n`;\n// Define a separate fragment for variable products with variations\nconst VARIABLE_PRODUCT_FRAGMENT = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  fragment VariableProductWithVariations on VariableProduct {\n    attributes {\n      nodes {\n        name\n        options\n      }\n    }\n    variations {\n      nodes {\n        id\n        databaseId\n        name\n        price\n        regularPrice\n        salePrice\n        stockStatus\n        stockQuantity\n        attributes {\n          nodes {\n            name\n            value\n          }\n        }\n      }\n    }\n  }\n`;\n// Queries\nconst GET_PRODUCTS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  query GetProducts(\n    $first: Int\n    $after: String\n    $where: RootQueryToProductConnectionWhereArgs\n  ) {\n    products(first: $first, after: $after, where: $where) {\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n      nodes {\n        ...ProductFields\n        ... on VariableProduct {\n          ...VariableProductWithVariations\n        }\n      }\n    }\n  }\n  ${PRODUCT_FRAGMENT}\n  ${VARIABLE_PRODUCT_FRAGMENT}\n`;\nconst GET_PRODUCT_BY_SLUG = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  query GetProductBySlug($slug: ID!) {\n    product(id: $slug, idType: SLUG) {\n      ...ProductFields\n      ... on VariableProduct {\n        ...VariableProductWithVariations\n      }\n    }\n  }\n  ${PRODUCT_FRAGMENT}\n  ${VARIABLE_PRODUCT_FRAGMENT}\n`;\nconst GET_PRODUCT_BY_SLUG_WITH_TAGS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  query GetProductBySlugWithTags($slug: ID!) {\n    product(id: $slug, idType: SLUG) {\n      ...ProductFields\n      ... on VariableProduct {\n        ...VariableProductWithVariations\n      }\n      productTags {\n        nodes {\n          id\n          name\n          slug\n        }\n      }\n      productCategories {\n        nodes {\n          id\n          name\n          slug\n        }\n      }\n    }\n  }\n  ${PRODUCT_FRAGMENT}\n  ${VARIABLE_PRODUCT_FRAGMENT}\n`;\nconst GET_CATEGORIES = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  query GetCategories(\n    $first: Int\n    $after: String\n    $where: RootQueryToProductCategoryConnectionWhereArgs\n  ) {\n    productCategories(first: $first, after: $after, where: $where) {\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n      nodes {\n        id\n        databaseId\n        name\n        slug\n        description\n        count\n        image {\n          sourceUrl\n          altText\n        }\n      }\n    }\n  }\n`;\n// Fetch functions\nasync function getProducts(variables = {}) {\n    try {\n        const data = await fetchFromWooCommerce(GET_PRODUCTS, {\n            first: variables.first || 12,\n            after: variables.after || null,\n            where: variables.where || {}\n        }, [\n            \"products\"\n        ], 60);\n        return data.products;\n    } catch (error) {\n        console.error(\"Error fetching products:\", error);\n        return {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    }\n}\n/**\n * Get variations for a variable product\n */ async function getProductVariations(productId) {\n    try {\n        const query = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n      query GetProductVariations($id: ID!) {\n        product(id: $id, idType: DATABASE_ID) {\n          ... on VariableProduct {\n            variations {\n              nodes {\n                id\n                databaseId\n                name\n                price\n                regularPrice\n                salePrice\n                stockStatus\n                attributes {\n                  nodes {\n                    name\n                    value\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    `;\n        const response = await fetchFromWooCommerce(query, {\n            id: productId\n        }, [\n            `product-${productId}`,\n            \"products\"\n        ], 60);\n        return response.product?.variations?.nodes || [];\n    } catch (error) {\n        console.error(\"Error fetching product variations:\", error);\n        return [];\n    }\n}\n/**\n * Get a product by its slug\n */ async function getProductBySlug(slug) {\n    try {\n        const data = await fetchFromWooCommerce(GET_PRODUCT_BY_SLUG, {\n            slug\n        }, [\n            `product-${slug}`,\n            \"products\"\n        ], 60);\n        const product = data.product;\n        // If it's a variable product, fetch variations separately\n        if (product && product.type === \"VARIABLE\") {\n            const variations = await getProductVariations(product.databaseId);\n            return {\n                ...product,\n                variations: {\n                    nodes: variations\n                }\n            };\n        }\n        return product;\n    } catch (error) {\n        console.error(\"Error fetching product by slug:\", error);\n        return null;\n    }\n}\nasync function getProductBySlugWithTags(slug) {\n    try {\n        const data = await fetchFromWooCommerce(GET_PRODUCT_BY_SLUG_WITH_TAGS, {\n            slug\n        }, [\n            `product-${slug}`,\n            \"products\"\n        ], 60);\n        return data.product;\n    } catch (error) {\n        console.error(`Error fetching product with slug ${slug}:`, error);\n        return null;\n    }\n}\n// Categories functionality is now handled by the more comprehensive getAllCategories function\n// Helper function to format price\nfunction formatPrice(price) {\n    const numericPrice = typeof price === \"string\" ? parseFloat(price) : price;\n    return numericPrice.toFixed(2);\n}\n/**\n * Fetch data from WooCommerce GraphQL API with caching and revalidation\n */ async function fetchFromWooCommerce(query, variables = {}, tags = [], revalidate = 60) {\n    try {\n        // Use different approaches for client and server\n        if (isClient) {\n            // When on client, use our proxy API route to avoid CORS issues\n            const proxyEndpoint = \"/api/graphql\";\n            // Build the fetch options with session token\n            const headers = {\n                \"Content-Type\": \"application/json\"\n            };\n            // Add session token if available\n            const sessionToken = getSessionToken();\n            if (sessionToken) {\n                headers[\"woocommerce-session\"] = `Session ${sessionToken}`;\n            }\n            const fetchOptions = {\n                method: \"POST\",\n                headers,\n                body: JSON.stringify({\n                    query,\n                    variables\n                })\n            };\n            // Make the fetch request through our proxy\n            const response = await fetch(proxyEndpoint, fetchOptions);\n            if (!response.ok) {\n                throw new Error(`GraphQL API responded with status ${response.status}`);\n            }\n            // Extract session token from response headers if available\n            const responseSessionHeader = response.headers.get(\"woocommerce-session\");\n            if (responseSessionHeader) {\n                const token = responseSessionHeader.replace(\"Session \", \"\");\n                setSessionToken(token);\n            }\n            const { data, errors } = await response.json();\n            if (errors) {\n                console.error(\"GraphQL Errors:\", errors);\n                throw new Error(errors[0].message);\n            }\n            return data;\n        } else {\n            // Server-side code can directly access the WooCommerce GraphQL endpoint\n            // Build the fetch options with cache control\n            const fetchOptions = {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    query,\n                    variables\n                }),\n                next: {}\n            };\n            // Add cache tags if provided\n            if (tags && tags.length > 0) {\n                fetchOptions.next.tags = tags;\n            }\n            // Add revalidation if provided\n            if (revalidate !== undefined) {\n                fetchOptions.next.revalidate = revalidate;\n            }\n            // Make the fetch request\n            const response = await fetch(wooConfig.graphqlUrl, fetchOptions);\n            if (!response.ok) {\n                throw new Error(`WooCommerce GraphQL API responded with status ${response.status}`);\n            }\n            const { data, errors } = await response.json();\n            if (errors) {\n                console.error(\"GraphQL Errors:\", errors);\n                throw new Error(errors[0].message);\n            }\n            return data;\n        }\n    } catch (error) {\n        console.error(\"Error fetching from WooCommerce:\", error);\n        throw error;\n    }\n}\n/**\n * Base implementation of WooCommerce fetch that can be used by other modules\n * This provides a standardized way to make WooGraphQL API requests with retry logic\n * \n * @param query GraphQL query to execute \n * @param variables Variables for the GraphQL query\n * @param retries Number of retries in case of failure\n * @param delay Delay between retries in milliseconds\n * @returns The fetched data\n */ async function wooGraphQLFetch({ query, variables }, retries = 3, delay = 1000) {\n    let attemptCount = 0;\n    let lastError = null;\n    while(attemptCount < retries){\n        try {\n            // Use fetchFromWooCommerce for the actual request, but ignore caching controls\n            // as this is the low-level function that might be used in different contexts\n            const data = await fetchFromWooCommerce(query, variables, [], 0);\n            return data;\n        } catch (error) {\n            lastError = error;\n            attemptCount++;\n            if (attemptCount < retries) {\n                console.log(`Retrying request (${attemptCount}/${retries}) after ${delay}ms`);\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n                // Exponential backoff\n                delay *= 2;\n            }\n        }\n    }\n    console.error(`Failed after ${retries} attempts:`, lastError);\n    throw lastError;\n}\n/**\n * Get products by category with cache tags for efficient revalidation\n * \n * @param slug The category slug\n * @param first Number of products to fetch\n * @param revalidate Revalidation period in seconds (optional)\n * @returns The category with products\n */ async function getCategoryProductsWithTags(slug, first = 20, revalidate = 60) {\n    try {\n        // Define cache tags for this category\n        const tags = [\n            `category-${slug}`,\n            \"categories\",\n            \"products\"\n        ];\n        // Fetch the category with tags\n        const data = await fetchFromWooCommerce(QUERY_CATEGORY_PRODUCTS, {\n            slug,\n            first\n        }, tags, revalidate);\n        return data?.productCategory || null;\n    } catch (error) {\n        console.error(`Error fetching category with slug ${slug}:`, error);\n        throw error;\n    }\n}\n// GraphQL query to fetch all products\nconst QUERY_ALL_PRODUCTS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  query GetAllProducts($first: Int = 20) {\n    products(first: $first) {\n      nodes {\n        id\n        databaseId\n        name\n        slug\n        description\n        shortDescription\n        productCategories {\n          nodes {\n            id\n            name\n            slug\n          }\n        }\n        ... on SimpleProduct {\n          price\n          regularPrice\n          salePrice\n          onSale\n          stockStatus\n          stockQuantity\n        }\n        ... on VariableProduct {\n          price\n          regularPrice\n          salePrice\n          onSale\n          stockStatus\n          variations {\n            nodes {\n              stockStatus\n              stockQuantity\n            }\n          }\n        }\n        image {\n          id\n          sourceUrl\n          altText\n        }\n        galleryImages {\n          nodes {\n            id\n            sourceUrl\n            altText\n          }\n        }\n        ... on VariableProduct {\n          attributes {\n            nodes {\n              name\n              options\n            }\n          }\n        }\n        ... on SimpleProduct {\n          attributes {\n            nodes {\n              name\n              options\n            }\n          }\n        }\n      }\n    }\n  }\n`;\n// GraphQL query to fetch products by category\nconst QUERY_CATEGORY_PRODUCTS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  query GetProductsByCategory($slug: ID!, $first: Int = 20) {\n    productCategory(id: $slug, idType: SLUG) {\n      id\n      name\n      slug\n      description\n      products(first: $first) {\n        nodes {\n          id\n          databaseId\n          name\n          slug\n          description\n          shortDescription\n          productCategories {\n            nodes {\n              id\n              name\n              slug\n            }\n          }\n          ... on SimpleProduct {\n            price\n            regularPrice\n            salePrice\n            onSale\n            stockStatus\n            stockQuantity\n          }\n          ... on VariableProduct {\n            price\n            regularPrice\n            salePrice\n            onSale\n            stockStatus\n            variations {\n              nodes {\n                stockStatus\n                stockQuantity\n              }\n            }\n          }\n          image {\n            id\n            sourceUrl\n            altText\n          }\n          galleryImages {\n            nodes {\n              id\n              sourceUrl\n              altText\n            }\n          }\n          ... on VariableProduct {\n            attributes {\n              nodes {\n                name\n                options\n              }\n            }\n          }\n          ... on SimpleProduct {\n            attributes {\n              nodes {\n                name\n                options\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n`;\n// GraphQL query to fetch all categories\nconst QUERY_ALL_CATEGORIES = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  query GetAllCategories($first: Int = 20) {\n    productCategories(first: $first) {\n      nodes {\n        id\n        databaseId\n        name\n        slug\n        description\n        count\n        image {\n          sourceUrl\n          altText\n        }\n        children {\n          nodes {\n            id\n            name\n            slug\n          }\n        }\n      }\n    }\n  }\n`;\n// GraphQL query to get cart contents - Updated for current WooGraphQL schema\nconst QUERY_GET_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  query GetCart {\n    cart {\n      contents {\n        nodes {\n          key\n          product {\n            node {\n              id\n              databaseId\n              name\n              slug\n              type\n              image {\n                sourceUrl\n                altText\n              }\n            }\n          }\n          variation {\n            node {\n              id\n              databaseId\n              name\n              attributes {\n                nodes {\n                  name\n                  value\n                }\n              }\n            }\n          }\n          quantity\n          total\n        }\n      }\n      subtotal\n      total\n      totalTax\n      isEmpty\n    }\n  }\n`;\n// Mutation for customer login\nconst MUTATION_LOGIN = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  mutation LoginUser($username: String!, $password: String!) {\n    login(input: {\n      clientMutationId: \"login\"\n      username: $username\n      password: $password\n    }) {\n      authToken\n      refreshToken\n      user {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n        nicename\n        nickname\n        username\n      }\n    }\n  }\n`;\n// Get cart query - WooCommerce automatically creates a cart when needed\nconst GET_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  query GetCart {\n    cart {\n      contents {\n        nodes {\n          key\n          product {\n            node {\n              id\n              databaseId\n              name\n              slug\n              type\n              image {\n                sourceUrl\n                altText\n              }\n            }\n          }\n          variation {\n            node {\n              id\n              databaseId\n              name\n              attributes {\n                nodes {\n                  name\n                  value\n                }\n              }\n            }\n          }\n          quantity\n          total\n        }\n      }\n      subtotal\n      total\n      totalTax\n      isEmpty\n      contentsCount\n    }\n  }\n`;\n// Add to cart mutation - Updated for current WooGraphQL schema\nconst ADD_TO_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  mutation AddToCart($productId: Int!, $variationId: Int, $quantity: Int, $extraData: String) {\n    addToCart(\n      input: {\n        clientMutationId: \"addToCart\"\n        productId: $productId\n        variationId: $variationId\n        quantity: $quantity\n        extraData: $extraData\n      }\n    ) {\n      cart {\n        contents {\n          nodes {\n            key\n            product {\n              node {\n                id\n                databaseId\n                name\n                slug\n                type\n                image {\n                  sourceUrl\n                  altText\n                }\n              }\n            }\n            variation {\n              node {\n                id\n                databaseId\n                name\n                attributes {\n                  nodes {\n                    name\n                    value\n                  }\n                }\n              }\n            }\n            quantity\n            total\n          }\n        }\n        subtotal\n        total\n        totalTax\n        isEmpty\n        contentsCount\n      }\n    }\n  }\n`;\n// Remove from cart mutation - Updated for current WooGraphQL schema\nconst MUTATION_REMOVE_FROM_CART = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  mutation RemoveItemsFromCart($keys: [ID]!, $all: Boolean) {\n    removeItemsFromCart(input: { keys: $keys, all: $all }) {\n      cart {\n        contents {\n          nodes {\n            key\n            product {\n              node {\n                id\n                databaseId\n                name\n                slug\n                type\n                image {\n                  sourceUrl\n                  altText\n                }\n              }\n            }\n            variation {\n              node {\n                id\n                databaseId\n                name\n                attributes {\n                  nodes {\n                    name\n                    value\n                  }\n                }\n              }\n            }\n            quantity\n            total\n          }\n        }\n        subtotal\n        total\n        totalTax\n        isEmpty\n      }\n    }\n  }\n`;\n// Shipping and payment related queries\nconst QUERY_SHIPPING_METHODS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  query GetShippingMethods {\n    shippingMethods {\n      nodes {\n        id\n        title\n        description\n        cost\n      }\n    }\n  }\n`;\nconst QUERY_PAYMENT_GATEWAYS = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  query GetPaymentGateways {\n    paymentGateways {\n      nodes {\n        id\n        title\n        description\n        enabled\n      }\n    }\n  }\n`;\n// Implement core API methods\n/**\n * Get all products with pagination\n */ async function getAllProducts(first = 20) {\n    try {\n        const data = await wooGraphQLFetch({\n            query: QUERY_ALL_PRODUCTS,\n            variables: {\n                first\n            }\n        });\n        return data?.products?.nodes || [];\n    } catch (error) {\n        console.error(\"Error fetching all products:\", error);\n        return [];\n    }\n}\n/**\n * Get all categories with pagination\n */ async function getAllCategories(first = 20) {\n    try {\n        console.log(`🔍 Fetching all categories with first: ${first}`);\n        console.log(`📡 Using GraphQL endpoint: ${process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\"}`);\n        const data = await wooGraphQLFetch({\n            query: QUERY_ALL_CATEGORIES,\n            variables: {\n                first\n            }\n        });\n        console.log(`📊 Raw categories response:`, JSON.stringify(data, null, 2));\n        const categories = data?.productCategories?.nodes || [];\n        console.log(`📂 Found ${categories.length} categories:`, categories.map((cat)=>({\n                name: cat.name,\n                slug: cat.slug,\n                id: cat.id,\n                databaseId: cat.databaseId,\n                count: cat.count\n            })));\n        return categories;\n    } catch (error) {\n        console.error(\"❌ Error fetching all categories:\", error);\n        // Log more details about the error\n        if (error instanceof Error) {\n            console.error(`Error message: ${error.message}`);\n            console.error(`Error stack: ${error.stack}`);\n        }\n        return [];\n    }\n}\n/**\n * Test GraphQL connection and list available categories\n */ async function testWooCommerceConnection() {\n    try {\n        console.log(\"\\uD83E\\uDDEA Testing WooCommerce GraphQL connection...\");\n        console.log(`📡 Endpoint: ${process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\"}`);\n        // Test basic connection with a simple query\n        const testQuery = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n      query TestConnection {\n        generalSettings {\n          title\n          url\n        }\n      }\n    `;\n        const testResult = await wooGraphQLFetch({\n            query: testQuery,\n            variables: {}\n        });\n        console.log(\"✅ Basic connection test result:\", testResult);\n        // Test categories\n        const categories = await getAllCategories(50);\n        console.log(`📂 Available categories (${categories.length}):`, categories);\n        // Test products\n        const products = await getAllProducts(10);\n        console.log(`📦 Available products (${products.length}):`, products?.slice(0, 3));\n        return {\n            connectionWorking: !!testResult,\n            categoriesCount: categories.length,\n            productsCount: products.length,\n            categories: categories.map((cat)=>({\n                    name: cat.name,\n                    slug: cat.slug,\n                    count: cat.count\n                })),\n            sampleProducts: products?.slice(0, 3).map((prod)=>({\n                    name: prod.name,\n                    slug: prod.slug\n                }))\n        };\n    } catch (error) {\n        console.error(\"❌ WooCommerce connection test failed:\", error);\n        return {\n            connectionWorking: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n}\n/**\n * Get product categories with pagination and filtering\n * @param variables Object containing:\n *   - first: Number of categories to return (default: 20)\n *   - after: Cursor for pagination\n *   - where: Filter criteria (parent, search, etc.)\n * @returns Object containing categories and pagination info\n */ async function getCategories(variables = {}) {\n    try {\n        const result = await wooGraphQLFetch({\n            query: QUERY_ALL_CATEGORIES,\n            variables: {\n                first: variables.first || 20,\n                after: variables.after || null,\n                where: variables.where || {}\n            }\n        });\n        return {\n            nodes: result.productCategories.nodes,\n            pageInfo: result.productCategories.pageInfo\n        };\n    } catch (error) {\n        console.error(\"Error fetching categories:\", error);\n        return {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    }\n}\n/**\n * Create a new cart by adding the first item - WooCommerce automatically creates cart\n */ async function createCart(items = []) {\n    try {\n        if (items.length === 0) {\n            // Just return an empty cart structure - WooCommerce will create cart when first item is added\n            return {\n                contents: {\n                    nodes: []\n                },\n                subtotal: \"0\",\n                total: \"0\",\n                totalTax: \"0\",\n                isEmpty: true,\n                contentsCount: 0\n            };\n        }\n        // Add the first item to create the cart\n        const firstItem = items[0];\n        const cart = await addToCart(\"\", [\n            firstItem\n        ]);\n        // Add remaining items if any\n        if (items.length > 1) {\n            for(let i = 1; i < items.length; i++){\n                await addToCart(\"\", [\n                    items[i]\n                ]);\n            }\n            // Get the updated cart\n            return await getCart();\n        }\n        return cart;\n    } catch (error) {\n        console.error(\"Error creating cart:\", error);\n        throw error;\n    }\n}\n/**\n * Get cart contents - Updated for current WooGraphQL schema\n */ async function getCart() {\n    try {\n        const data = await wooGraphQLFetch({\n            query: GET_CART,\n            variables: {} // Cart query doesn't need parameters in current WooGraphQL\n        });\n        return data?.cart || null;\n    } catch (error) {\n        console.error(`Error fetching cart:`, error);\n        return null;\n    }\n}\n/**\n * Add items to cart - Updated for current WooGraphQL schema\n */ async function addToCart(_cartId, items) {\n    try {\n        // WooCommerce GraphQL addToCart only accepts one item at a time\n        // So we'll add the first item and return the cart\n        if (items.length === 0) {\n            throw new Error(\"No items provided to add to cart\");\n        }\n        const item = items[0];\n        const variables = {\n            productId: parseInt(item.productId),\n            quantity: item.quantity || 1,\n            variationId: item.variationId ? parseInt(item.variationId) : null,\n            extraData: null\n        };\n        console.log(\"Adding to cart with variables:\", variables);\n        const response = await wooGraphQLFetch({\n            query: ADD_TO_CART,\n            variables\n        });\n        console.log(\"Add to cart response:\", response);\n        return response.addToCart.cart;\n    } catch (error) {\n        console.error(`Error adding items to cart:`, error);\n        throw error;\n    }\n}\n/**\n * Remove items from cart - Updated for current WooGraphQL schema\n */ async function removeFromCart(cartId, keys) {\n    try {\n        const data = await wooGraphQLFetch({\n            query: MUTATION_REMOVE_FROM_CART,\n            variables: {\n                keys,\n                all: false\n            }\n        });\n        return data?.removeItemsFromCart?.cart || null;\n    } catch (error) {\n        console.error(`Error removing items from cart:`, error);\n        throw error;\n    }\n}\n/**\n * Customer login with WooCommerce GraphQL\n * \n * @param username User's email/username\n * @param password User's password\n * @returns Authentication token and user information\n */ async function customerLogin(username, password) {\n    try {\n        const LOGIN_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n      mutation LoginUser($username: String!, $password: String!) {\n        login(input: {\n          clientMutationId: \"login\"\n          username: $username\n          password: $password\n        }) {\n          authToken\n          refreshToken\n          user {\n            id\n            databaseId\n            email\n            firstName\n            lastName\n            nicename\n            nickname\n            username\n          }\n        }\n      }\n    `;\n        const variables = {\n            username,\n            password\n        };\n        const result = await wooGraphQLFetch({\n            query: LOGIN_MUTATION,\n            variables\n        });\n        if (!result || !result.login || !result.login.authToken) {\n            throw new Error(\"Login failed: Invalid response from server\");\n        }\n        // Set the auth token for future requests\n        setAuthToken(result.login.authToken);\n        return {\n            authToken: result.login.authToken,\n            refreshToken: result.login.refreshToken,\n            user: result.login.user,\n            customerUserErrors: [] // For compatibility with Shopify auth\n        };\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        // Format the error to match the expected structure\n        return {\n            authToken: null,\n            refreshToken: null,\n            user: null,\n            customerUserErrors: [\n                {\n                    code: \"LOGIN_FAILED\",\n                    message: error.message || \"Login failed. Please check your credentials.\"\n                }\n            ]\n        };\n    }\n}\n/**\n * Create customer (register) with WooCommerce GraphQL\n */ async function createCustomer({ email, password, firstName, lastName, phone, acceptsMarketing = false }) {\n    try {\n        const REGISTER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n      mutation RegisterUser($input: RegisterCustomerInput!) {\n        registerCustomer(input: $input) {\n          clientMutationId\n          authToken\n          refreshToken\n          customer {\n            id\n            databaseId\n            email\n            firstName\n            lastName\n          }\n        }\n      }\n    `;\n        const variables = {\n            input: {\n                clientMutationId: \"registerCustomer\",\n                email,\n                password,\n                firstName,\n                lastName,\n                username: email\n            }\n        };\n        const result = await wooGraphQLFetch({\n            query: REGISTER_MUTATION,\n            variables\n        });\n        if (!result || !result.registerCustomer) {\n            throw new Error(\"Registration failed: Invalid response from server\");\n        }\n        return {\n            customer: result.registerCustomer.customer,\n            authToken: result.registerCustomer.authToken,\n            refreshToken: result.registerCustomer.refreshToken,\n            customerUserErrors: [] // For compatibility with Shopify auth\n        };\n    } catch (error) {\n        console.error(\"Registration error:\", error);\n        // Format the error to match the expected structure\n        return {\n            customer: null,\n            authToken: null,\n            refreshToken: null,\n            customerUserErrors: [\n                {\n                    code: \"REGISTRATION_FAILED\",\n                    message: error.message || \"Registration failed. Please try again.\"\n                }\n            ]\n        };\n    }\n}\n/**\n * Get customer data using JWT authentication\n * \n * @param token JWT auth token\n * @returns Customer data\n */ async function getCustomer(token) {\n    try {\n        if (token) {\n            setAuthToken(token);\n        }\n        const GET_CUSTOMER_QUERY = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n      query GetCustomer {\n        customer {\n          id\n          databaseId\n          email\n          firstName\n          lastName\n          displayName\n          username\n          role\n          date\n          modified\n          isPayingCustomer\n          orderCount\n          totalSpent\n          billing {\n            firstName\n            lastName\n            company\n            address1\n            address2\n            city\n            state\n            postcode\n            country\n            email\n            phone\n          }\n          shipping {\n            firstName\n            lastName\n            company\n            address1\n            address2\n            city\n            state\n            postcode\n            country\n          }\n          orders(first: 50) {\n            nodes {\n              id\n              databaseId\n              date\n              status\n              total\n              subtotal\n              totalTax\n              shippingTotal\n              discountTotal\n              paymentMethodTitle\n              customerNote\n              billing {\n                firstName\n                lastName\n                company\n                address1\n                address2\n                city\n                state\n                postcode\n                country\n                email\n                phone\n              }\n              shipping {\n                firstName\n                lastName\n                company\n                address1\n                address2\n                city\n                state\n                postcode\n                country\n              }\n              lineItems {\n                nodes {\n                  product {\n                    node {\n                      id\n                      name\n                      slug\n                      image {\n                        sourceUrl\n                        altText\n                      }\n                    }\n                  }\n                  variation {\n                    node {\n                      id\n                      name\n                      attributes {\n                        nodes {\n                          name\n                          value\n                        }\n                      }\n                    }\n                  }\n                  quantity\n                  total\n                  subtotal\n                  totalTax\n                }\n              }\n              shippingLines {\n                nodes {\n                  methodTitle\n                  total\n                }\n              }\n              feeLines {\n                nodes {\n                  name\n                  total\n                }\n              }\n              couponLines {\n                nodes {\n                  code\n                  discount\n                }\n              }\n            }\n          }\n          downloadableItems {\n            nodes {\n              name\n              downloadId\n              downloadsRemaining\n              accessExpires\n              product {\n                node {\n                  id\n                  name\n                }\n              }\n            }\n          }\n          metaData {\n            key\n            value\n          }\n        }\n      }\n    `;\n        const result = await wooGraphQLFetch({\n            query: GET_CUSTOMER_QUERY\n        });\n        if (!result || !result.customer) {\n            throw new Error(\"Failed to get customer data\");\n        }\n        return result.customer;\n    } catch (error) {\n        console.error(\"Error getting customer data:\", error);\n        throw error;\n    } finally{\n        if (token) {\n            clearAuthToken();\n        }\n    }\n}\n/**\n * Normalize product data to match the existing frontend structure\n * This helps maintain compatibility with the existing components\n */ function normalizeProduct(product) {\n    if (!product) return null;\n    // Extract product type\n    const isVariable = Boolean(product.variations?.nodes?.length);\n    // Extract pricing data\n    let priceRange = {\n        minVariantPrice: {\n            amount: product.price || \"0\",\n            currencyCode: \"INR\" // Default currency for the application\n        },\n        maxVariantPrice: {\n            amount: product.price || \"0\",\n            currencyCode: \"INR\"\n        }\n    };\n    // For variable products, calculate actual price range\n    if (isVariable && product.variations?.nodes?.length > 0) {\n        const prices = product.variations.nodes.map((variant)=>parseFloat(variant.price || \"0\")).filter((price)=>!isNaN(price));\n        if (prices.length > 0) {\n            priceRange = {\n                minVariantPrice: {\n                    amount: String(Math.min(...prices)),\n                    currencyCode: \"INR\"\n                },\n                maxVariantPrice: {\n                    amount: String(Math.max(...prices)),\n                    currencyCode: \"INR\"\n                }\n            };\n        }\n    }\n    // Extract and normalize images\n    const images = normalizeProductImages(product);\n    // Extract variant data\n    const variants = product.variations?.nodes?.map((variant)=>({\n            id: variant.id,\n            title: variant.name,\n            price: {\n                amount: variant.price || \"0\",\n                currencyCode: \"USD\"\n            },\n            availableForSale: variant.stockStatus === \"IN_STOCK\",\n            selectedOptions: variant.attributes?.nodes?.map((attr)=>({\n                    name: attr.name,\n                    value: attr.value\n                })) || [],\n            sku: variant.sku || \"\",\n            image: variant.image ? {\n                url: variant.image.sourceUrl,\n                altText: variant.image.altText || \"\"\n            } : null\n        })) || [];\n    // Extract options data for variable products\n    const options = product.attributes?.nodes?.map((attr)=>({\n            name: attr.name,\n            values: attr.options || []\n        })) || [];\n    // Extract category data\n    const collections = product.productCategories?.nodes?.map((category)=>({\n            handle: category.slug,\n            title: category.name\n        })) || [];\n    // Extract meta fields for custom data\n    const metafields = {};\n    if (product.metafields) {\n        product.metafields.forEach((meta)=>{\n            metafields[meta.key] = meta.value;\n        });\n    }\n    // Return normalized product object that matches existing frontend structure\n    return {\n        id: product.id,\n        handle: product.slug,\n        title: product.name,\n        description: product.description || \"\",\n        descriptionHtml: product.description || \"\",\n        priceRange,\n        options,\n        variants,\n        images,\n        collections,\n        availableForSale: product.stockStatus !== \"OUT_OF_STOCK\",\n        metafields,\n        // Add original data for reference if needed\n        _originalWooProduct: product\n    };\n}\n/**\n * Normalize product images array\n */ function normalizeProductImages(product) {\n    const images = [];\n    // Add main product image if available\n    if (product.image) {\n        images.push({\n            url: product.image.sourceUrl,\n            altText: product.image.altText || product.name || \"\"\n        });\n    }\n    // Add gallery images if available\n    if (product.galleryImages?.nodes?.length) {\n        product.galleryImages.nodes.forEach((img)=>{\n            // Avoid duplicating the main image if it's already in the gallery\n            const isMainImage = product.image && img.sourceUrl === product.image.sourceUrl;\n            if (!isMainImage) {\n                images.push({\n                    url: img.sourceUrl,\n                    altText: img.altText || product.name || \"\"\n                });\n            }\n        });\n    }\n    return images;\n}\n/**\n * Normalize category data to match existing frontend structure\n */ function normalizeCategory(category) {\n    if (!category) return null;\n    return {\n        id: category.id,\n        handle: category.slug,\n        title: category.name,\n        description: category.description || \"\",\n        image: category.image ? {\n            url: category.image.sourceUrl,\n            altText: category.image.altText || \"\"\n        } : null,\n        products: category.products?.nodes?.map(normalizeProduct) || []\n    };\n}\n/**\n * Get custom meta field from product\n */ const getMetafield = (product, key, namespace, defaultValue = \"\")=>{\n    if (!product || !product.metafields) return defaultValue;\n    // Find the meta field by key\n    if (namespace) {\n        const metaKey = `${namespace}:${key}`;\n        return product.metafields[metaKey] || defaultValue;\n    }\n    return product.metafields[key] || defaultValue;\n};\n/**\n * Normalize cart data to match existing frontend structure\n */ function normalizeCart(cart) {\n    if (!cart) return null;\n    const lineItems = cart.contents?.nodes?.map((item)=>{\n        const product = item.product?.node;\n        const variation = item.variation?.node;\n        return {\n            id: item.key,\n            quantity: item.quantity,\n            merchandise: {\n                id: variation?.id || product?.id,\n                title: variation?.name || product?.name,\n                product: {\n                    id: product?.id,\n                    handle: product?.slug,\n                    title: product?.name,\n                    image: product?.image ? {\n                        url: product?.image.sourceUrl,\n                        altText: product?.image.altText || \"\"\n                    } : null\n                },\n                selectedOptions: variation?.attributes?.nodes?.map((attr)=>({\n                        name: attr.name,\n                        value: attr.value\n                    })) || []\n            },\n            cost: {\n                totalAmount: {\n                    amount: item.total || \"0\",\n                    currencyCode: \"USD\"\n                }\n            }\n        };\n    }) || [];\n    const discountCodes = cart.appliedCoupons?.nodes?.map((coupon)=>({\n            code: coupon.code,\n            amount: coupon.discountAmount || \"0\"\n        })) || [];\n    // Calculate total quantity from line items instead of using contentsCount\n    const totalQuantity = lineItems.reduce((sum, item)=>sum + item.quantity, 0);\n    return {\n        id: cart.id,\n        checkoutUrl: \"\",\n        totalQuantity: totalQuantity,\n        cost: {\n            subtotalAmount: {\n                amount: cart.subtotal || \"0\",\n                currencyCode: \"USD\"\n            },\n            totalAmount: {\n                amount: cart.total || \"0\",\n                currencyCode: \"USD\"\n            }\n        },\n        lines: lineItems,\n        discountCodes\n    };\n}\n/**\n * Generates a checkout URL for WooCommerce\n * \n * @param cartId The cart ID to associate with checkout\n * @param isLoggedIn Whether the user is logged in\n * @returns The WooCommerce checkout URL\n */ function getWooCommerceCheckoutUrl(cartId, isLoggedIn = false) {\n    // Base checkout URL\n    const baseUrl = `${wooConfig.storeUrl}/checkout`;\n    // Add cart parameter if needed\n    const cartParam = cartId ? `?cart=${cartId}` : \"\";\n    // Add comprehensive guest checkout parameters to ensure login is bypassed\n    // These parameters will work across different WooCommerce configurations and plugins\n    let guestParams = \"\";\n    if (!isLoggedIn) {\n        const separator = cartParam ? \"&\" : \"?\";\n        guestParams = `${separator}guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1`;\n    }\n    // Construct the full URL\n    return `${baseUrl}${cartParam}${guestParams}`;\n}\n/**\n * Get a product by its ID\n * @param id The product ID\n * @param revalidate Revalidation time in seconds\n * @returns The product data or a fallback product if not found\n */ async function getProductById(id, revalidate = 60) {\n    try {\n        // Check if ID is in a valid format\n        if (!id || id === \"undefined\" || id === \"null\") {\n            console.warn(`Invalid product ID format: ${id}, returning fallback product`);\n            return createFallbackProduct(id);\n        }\n        // Validate and transform the product ID\n        const validatedId = await (0,_wooInventoryMapping__WEBPACK_IMPORTED_MODULE_1__.validateProductId)(id);\n        // Define cache tags for this product\n        const tags = [\n            `product-${validatedId}`,\n            \"products\",\n            \"inventory\"\n        ];\n        // Define the query\n        const QUERY_PRODUCT_BY_ID = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n      query GetProductById($id: ID!) {\n        product(id: $id, idType: DATABASE_ID) {\n          id\n          databaseId\n          name\n          slug\n          description\n          shortDescription\n          productCategories {\n            nodes {\n              id\n              name\n              slug\n            }\n          }\n          ... on SimpleProduct {\n            price\n            regularPrice\n            salePrice\n            onSale\n            stockStatus\n            stockQuantity\n          }\n          ... on VariableProduct {\n            price\n            regularPrice\n            salePrice\n            onSale\n            stockStatus\n            variations {\n              nodes {\n                stockStatus\n                stockQuantity\n              }\n            }\n          }\n          image {\n            id\n            sourceUrl\n            altText\n          }\n        }\n      }\n    `;\n        try {\n            // Fetch the product with tags\n            const data = await fetchFromWooCommerce(QUERY_PRODUCT_BY_ID, {\n                id: validatedId\n            }, tags, revalidate);\n            // Check if product exists\n            if (!data?.product) {\n                console.warn(`No product found with ID: ${id}, returning fallback product`);\n                return createFallbackProduct(id);\n            }\n            return data.product;\n        } catch (error) {\n            console.error(`Error fetching product with ID ${id}:`, error);\n            // Return a fallback product instead of throwing an error\n            return createFallbackProduct(id);\n        }\n    } catch (error) {\n        console.error(`Error in getProductById for ID ${id}:`, error);\n        // Return a fallback product instead of throwing an error\n        return createFallbackProduct(id);\n    }\n}\n/**\n * Create a fallback product for when a product cannot be found\n * @param id The original product ID\n * @returns A fallback product object\n */ function createFallbackProduct(id) {\n    return {\n        id: id,\n        databaseId: 0,\n        name: \"Product Not Found\",\n        slug: \"product-not-found\",\n        description: \"This product is no longer available.\",\n        shortDescription: \"Product not found\",\n        price: \"0.00\",\n        regularPrice: \"0.00\",\n        salePrice: null,\n        onSale: false,\n        stockStatus: \"OUT_OF_STOCK\",\n        stockQuantity: 0,\n        image: {\n            id: null,\n            sourceUrl: \"/placeholder-product.jpg\",\n            altText: \"Product not found\"\n        },\n        productCategories: {\n            nodes: []\n        }\n    };\n}\n/**\n * Search products by keyword with advanced options\n * @param query Search query\n * @param options Search options including pagination, sorting, filtering\n * @returns Products matching the search query\n */ async function searchProducts(query, options = {}) {\n    // Handle case where options is passed as a number for backward compatibility\n    const first = typeof options === \"number\" ? options : options.first || 10;\n    const searchQuery = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n    query SearchProducts($query: String!, $first: Int) {\n      products(first: $first, where: { search: $query }) {\n        nodes {\n          id\n          databaseId\n          name\n          slug\n          price\n          image {\n            sourceUrl\n            altText\n          }\n          shortDescription\n        }\n        pageInfo {\n          hasNextPage\n          endCursor\n        }\n      }\n    }\n  `;\n    try {\n        const data = await graphQLClient.request(searchQuery, {\n            query,\n            first\n        });\n        return data?.products || {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    } catch (error) {\n        console.error(\"Error searching products:\", error);\n        return {\n            nodes: [],\n            pageInfo: {\n                hasNextPage: false,\n                endCursor: null\n            }\n        };\n    }\n}\n/**\n * Get a single product by ID\n * @param id Product ID\n * @returns Product data\n */ async function getProduct(id) {\n    const productQuery = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n    query GetProduct($id: ID!) {\n      product(id: $id, idType: DATABASE_ID) {\n        id\n        databaseId\n        name\n        slug\n        description\n        shortDescription\n        price\n        regularPrice\n        salePrice\n        onSale\n        stockStatus\n        stockQuantity\n        image {\n          sourceUrl\n          altText\n        }\n        galleryImages {\n          nodes {\n            sourceUrl\n            altText\n          }\n        }\n        ... on SimpleProduct {\n          attributes {\n            nodes {\n              name\n              options\n            }\n          }\n          price\n          regularPrice\n          salePrice\n        }\n        ... on VariableProduct {\n          price\n          regularPrice\n          salePrice\n          attributes {\n            nodes {\n              name\n              options\n            }\n          }\n          variations {\n            nodes {\n              id\n              databaseId\n              name\n              price\n              regularPrice\n              salePrice\n              stockStatus\n              attributes {\n                nodes {\n                  name\n                  value\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  `;\n    try {\n        const data = await graphQLClient.request(productQuery, {\n            id\n        });\n        return data.product;\n    } catch (error) {\n        console.error(\"Error fetching product:\", error);\n        throw new Error(\"Failed to fetch product\");\n    }\n}\nasync function getCategoryProducts(slug, options = {}) {\n    try {\n        const { first = 20 } = options;\n        console.log(`🔍 Fetching category products for slug: \"${slug}\" with first: ${first}`);\n        console.log(`📡 Using GraphQL endpoint: ${process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\"}`);\n        const data = await wooGraphQLFetch({\n            query: QUERY_CATEGORY_PRODUCTS,\n            variables: {\n                slug,\n                first\n            }\n        });\n        console.log(`📊 Raw response for category \"${slug}\":`, JSON.stringify(data, null, 2));\n        if (!data?.productCategory) {\n            console.log(`⚠️ No productCategory found in response for slug: \"${slug}\"`);\n            // Try alternative approach - search by ID if slug doesn't work\n            if (slug && !isNaN(Number(slug))) {\n                console.log(`🔄 Trying to fetch category by ID: ${slug}`);\n                const dataById = await wooGraphQLFetch({\n                    query: QUERY_CATEGORY_PRODUCTS.replace(\"idType: SLUG\", \"idType: DATABASE_ID\"),\n                    variables: {\n                        slug: Number(slug),\n                        first\n                    }\n                });\n                console.log(`📊 Response by ID:`, JSON.stringify(dataById, null, 2));\n                return dataById?.productCategory || null;\n            }\n        }\n        return data?.productCategory || null;\n    } catch (error) {\n        console.error(`❌ Error fetching category products with slug ${slug}:`, error);\n        // Log more details about the error\n        if (error instanceof Error) {\n            console.error(`Error message: ${error.message}`);\n            console.error(`Error stack: ${error.stack}`);\n        }\n        return null;\n    }\n}\n/**\n * Get products by category - alias for getCategoryProducts for compatibility\n */ async function getProductsByCategory(categorySlug, options = {}) {\n    const { limit = 20, page = 1, sort = \"DATE\" } = options;\n    const after = page > 1 ? btoa(`arrayconnection:${(page - 1) * limit - 1}`) : undefined;\n    return getCategoryProducts(categorySlug, {\n        first: limit,\n        after,\n        orderby: sort,\n        order: \"DESC\"\n    });\n}\n/**\n * Get products by tag\n */ async function getProductsByTag(tagSlug, options = {}) {\n    const { limit = 20, page = 1, sort = \"DATE\" } = options;\n    // For now, return empty result as tag functionality needs proper GraphQL query\n    // This prevents build errors while maintaining API compatibility\n    console.warn(`getProductsByTag called with tag: ${tagSlug} - functionality not yet implemented`);\n    return {\n        tag: {\n            id: \"\",\n            name: tagSlug,\n            slug: tagSlug,\n            description: \"\"\n        },\n        products: [],\n        pageInfo: {\n            hasNextPage: false,\n            endCursor: null\n        }\n    };\n}\n// Customer Mutations\nconst CREATE_CUSTOMER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  mutation CreateCustomer($input: RegisterCustomerInput!) {\n    registerCustomer(input: $input) {\n      customer {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n        displayName\n      }\n      authToken\n      refreshToken\n    }\n  }\n`;\nconst UPDATE_CUSTOMER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  mutation UpdateCustomer($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      clientMutationId\n      customer {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n        displayName\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n      customerUserErrors {\n        field\n        message\n      }\n    }\n  }\n`;\nconst GET_CUSTOMER_QUERY = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  query GetCustomer {\n    customer {\n      id\n      databaseId\n      email\n      firstName\n      lastName\n      displayName\n      username\n      role\n      date\n      modified\n      isPayingCustomer\n      orderCount\n      totalSpent\n      billing {\n        firstName\n        lastName\n        company\n        address1\n        address2\n        city\n        state\n        postcode\n        country\n        email\n        phone\n      }\n      shipping {\n        firstName\n        lastName\n        company\n        address1\n        address2\n        city\n        state\n        postcode\n        country\n      }\n      orders(first: 50) {\n        nodes {\n          id\n          databaseId\n          date\n          status\n          total\n          subtotal\n          totalTax\n          shippingTotal\n          discountTotal\n          paymentMethodTitle\n          customerNote\n          billing {\n            firstName\n            lastName\n            company\n            address1\n            address2\n            city\n            state\n            postcode\n            country\n            email\n            phone\n          }\n          shipping {\n            firstName\n            lastName\n            company\n            address1\n            address2\n            city\n            state\n            postcode\n            country\n          }\n          lineItems {\n            nodes {\n              product {\n                node {\n                  id\n                  name\n                  slug\n                  image {\n                    sourceUrl\n                    altText\n                  }\n                }\n              }\n              variation {\n                node {\n                  id\n                  name\n                  attributes {\n                    nodes {\n                      name\n                      value\n                    }\n                  }\n                }\n              }\n              quantity\n              total\n              subtotal\n              totalTax\n            }\n          }\n          shippingLines {\n            nodes {\n              methodTitle\n              total\n            }\n          }\n          feeLines {\n            nodes {\n              name\n              total\n            }\n          }\n          couponLines {\n            nodes {\n              code\n              discount\n            }\n          }\n        }\n      }\n      downloadableItems {\n        nodes {\n          name\n          downloadId\n          downloadsRemaining\n          accessExpires\n          product {\n            node {\n              id\n              name\n            }\n          }\n        }\n      }\n      metaData {\n        key\n        value\n      }\n    }\n  }\n`;\nconst CREATE_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  mutation CreateAddress($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      customer {\n        id\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n    }\n  }\n`;\nconst UPDATE_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  mutation UpdateAddress($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      customer {\n        id\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n    }\n  }\n`;\nconst DELETE_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  mutation DeleteAddress($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      customer {\n        id\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n    }\n  }\n`;\nconst SET_DEFAULT_ADDRESS_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  mutation SetDefaultAddress($input: UpdateCustomerInput!) {\n    updateCustomer(input: $input) {\n      customer {\n        id\n        billing {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n          email\n          phone\n        }\n        shipping {\n          firstName\n          lastName\n          company\n          address1\n          address2\n          city\n          state\n          postcode\n          country\n        }\n      }\n    }\n  }\n`;\nconst UPDATE_CART_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_0__.gql)`\n  mutation UpdateCart($input: UpdateItemQuantitiesInput!) {\n    updateItemQuantities(input: $input) {\n      cart {\n        contents {\n          nodes {\n            key\n            product {\n              node {\n                id\n                name\n                price\n              }\n            }\n            quantity\n            total\n          }\n        }\n        subtotal\n        total\n        totalTax\n        isEmpty\n      }\n    }\n  }\n`;\n/**\n * Update customer profile\n */ async function updateCustomer(token, customerData) {\n    try {\n        console.log(\"Updating customer with data:\", customerData);\n        console.log(\"Using token:\", token ? \"Token present\" : \"No token\");\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_0__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": `Bearer ${token}`\n            }\n        });\n        const variables = {\n            input: {\n                clientMutationId: \"updateCustomer\",\n                ...customerData\n            }\n        };\n        console.log(\"GraphQL variables:\", variables);\n        const response = await client.request(UPDATE_CUSTOMER_MUTATION, variables);\n        console.log(\"GraphQL response:\", response);\n        if (response.updateCustomer.customerUserErrors && response.updateCustomer.customerUserErrors.length > 0) {\n            const errorMessage = response.updateCustomer.customerUserErrors[0].message;\n            console.error(\"Customer update error:\", errorMessage);\n            throw new Error(errorMessage);\n        }\n        return response.updateCustomer;\n    } catch (error) {\n        console.error(\"Error updating customer:\", error);\n        throw error;\n    }\n}\n/**\n * Create a new address for the customer\n */ async function createAddress(token, address) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_0__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": `Bearer ${token}`\n            }\n        });\n        // Determine if this is a billing or shipping address\n        const addressType = address.addressType || \"shipping\";\n        const variables = {\n            input: {\n                [`${addressType}`]: {\n                    firstName: address.firstName || \"\",\n                    lastName: address.lastName || \"\",\n                    company: address.company || \"\",\n                    address1: address.address1 || \"\",\n                    address2: address.address2 || \"\",\n                    city: address.city || \"\",\n                    state: address.province || \"\",\n                    postcode: address.zip || \"\",\n                    country: address.country || \"\",\n                    ...addressType === \"billing\" ? {\n                        email: address.email || \"\",\n                        phone: address.phone || \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(CREATE_ADDRESS_MUTATION, variables);\n        return {\n            customerAddress: response.updateCustomer.customer[addressType],\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error creating address:\", error);\n        throw error;\n    }\n}\n/**\n * Update an existing address\n */ async function updateAddress(token, id, address) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_0__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": `Bearer ${token}`\n            }\n        });\n        // Determine if this is a billing or shipping address\n        const addressType = address.addressType || \"shipping\";\n        const variables = {\n            input: {\n                [`${addressType}`]: {\n                    firstName: address.firstName || \"\",\n                    lastName: address.lastName || \"\",\n                    company: address.company || \"\",\n                    address1: address.address1 || \"\",\n                    address2: address.address2 || \"\",\n                    city: address.city || \"\",\n                    state: address.province || \"\",\n                    postcode: address.zip || \"\",\n                    country: address.country || \"\",\n                    ...addressType === \"billing\" ? {\n                        email: address.email || \"\",\n                        phone: address.phone || \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(UPDATE_ADDRESS_MUTATION, variables);\n        return {\n            customerAddress: response.updateCustomer.customer[addressType],\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error updating address:\", error);\n        throw error;\n    }\n}\n/**\n * Delete an address\n * Note: In WooCommerce, we don't actually delete addresses but clear them\n */ async function deleteAddress(token, id) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_0__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": `Bearer ${token}`\n            }\n        });\n        // Get the current customer to determine which address to clear\n        const customer = await getCustomer(token);\n        // Determine if this is a billing or shipping address\n        // In this implementation, we're using the id to determine which address to clear\n        // This is a simplification - you might need a different approach\n        const addressType = id === \"billing\" ? \"billing\" : \"shipping\";\n        const variables = {\n            input: {\n                [`${addressType}`]: {\n                    firstName: \"\",\n                    lastName: \"\",\n                    company: \"\",\n                    address1: \"\",\n                    address2: \"\",\n                    city: \"\",\n                    state: \"\",\n                    postcode: \"\",\n                    country: \"\",\n                    ...addressType === \"billing\" ? {\n                        email: customer.email || \"\",\n                        phone: \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(DELETE_ADDRESS_MUTATION, variables);\n        return {\n            deletedCustomerAddressId: id,\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error deleting address:\", error);\n        throw error;\n    }\n}\n/**\n * Set default address\n * Note: In WooCommerce, the concept of \"default\" address is different\n * This implementation copies the address from one type to another\n */ async function setDefaultAddress(token, addressId) {\n    try {\n        // Create a new client with the auth token\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_0__.GraphQLClient(endpoint, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\",\n                \"Authorization\": `Bearer ${token}`\n            }\n        });\n        // Get the current customer\n        const customer = await getCustomer(token);\n        // Determine source and target address types\n        // This is a simplification - you might need a different approach\n        const sourceType = addressId === \"billing\" ? \"billing\" : \"shipping\";\n        const targetType = sourceType === \"billing\" ? \"shipping\" : \"billing\";\n        // Copy the address from source to target\n        const sourceAddress = customer[sourceType];\n        const variables = {\n            input: {\n                [`${targetType}`]: {\n                    ...sourceAddress,\n                    ...targetType === \"billing\" ? {\n                        email: customer.email || \"\",\n                        phone: sourceAddress.phone || \"\"\n                    } : {}\n                }\n            }\n        };\n        const response = await client.request(SET_DEFAULT_ADDRESS_MUTATION, variables);\n        return {\n            customer: response.updateCustomer.customer,\n            customerUserErrors: []\n        };\n    } catch (error) {\n        console.error(\"Error setting default address:\", error);\n        throw error;\n    }\n}\n/**\n * Update cart items\n */ async function updateCart(items) {\n    try {\n        const variables = {\n            input: {\n                items\n            }\n        };\n        const response = await wooGraphQLFetch({\n            query: UPDATE_CART_MUTATION,\n            variables\n        });\n        return response.updateItemQuantities.cart;\n    } catch (error) {\n        console.error(\"Error updating cart:\", error);\n        throw error;\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/woocommerce.ts\n");

/***/ })

};
;