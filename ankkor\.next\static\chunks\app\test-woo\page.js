/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/test-woo/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Capp%5C%5Ctest-woo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!**********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Capp%5C%5Ctest-woo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \**********************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/test-woo/page.tsx */ \"(app-pages-browser)/./src/app/test-woo/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2Fua2tvcndvbyU1QyU1Q2Fua2tvciU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Rlc3Qtd29vJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBd0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz8yOGVmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcYW5ra29yd29vXFxcXGFua2tvclxcXFxzcmNcXFxcYXBwXFxcXHRlc3Qtd29vXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Capp%5C%5Ctest-woo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/test-woo/page.tsx":
/*!***********************************!*\
  !*** ./src/app/test-woo/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestWooCommercePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction TestWooCommercePage() {\n    var _testResults_categories, _testResults_alternativeTests;\n    _s();\n    const [testResults, setTestResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const runTests = async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                console.log(\"\\uD83E\\uDDEA Starting WooCommerce tests...\");\n                // Import the test function\n                const { testWooCommerceConnection, getAllCategories, getCategoryProducts } = await Promise.all(/*! import() */[__webpack_require__.e(\"framework-node_modules_next_dist_a\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_a\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_ap\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_b\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_layout-router_js-4906aef6\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_m\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_p\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_C\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_LeftRightDi-d5fdd2e0\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_O\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_Overlay_mai-e776ae3b\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_Te\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_V\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_react-dev-overlay_internal_container_B\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_react-dev-overlay_internal_container_R\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_f\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_h\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_react-dev-overlay_internal_h\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_react-dev-overlay_internal_styles_B\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_rea\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_re\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_router-reducer_co\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_router-reducer_fe\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_router-reducer_h\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_router-reducer_pp\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_router-reducer_reducers_f\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_router-reducer_reducers_r\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_components_router-reducer_r\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_c\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_g\"), __webpack_require__.e(\"framework-node_modules_next_dist_client_l\"), __webpack_require__.e(\"framework-node_modules_next_dist_compiled_a\"), __webpack_require__.e(\"framework-node_modules_next_dist_compiled_m\"), __webpack_require__.e(\"framework-node_modules_next_dist_compiled_react-dom_cjs_react-dom_development_js-3041f41d\"), __webpack_require__.e(\"framework-node_modules_next_dist_compiled_react-d\"), __webpack_require__.e(\"framework-node_modules_next_dist_compiled_react-server-dom-webpack_cjs_react-server-dom-webpack-clie-4912d8da\"), __webpack_require__.e(\"framework-node_modules_next_dist_compiled_react_cjs_react-jsx-dev-runtime_development_js-12999a20\"), __webpack_require__.e(\"framework-node_modules_next_dist_compiled_react_c\"), __webpack_require__.e(\"framework-node_modules_next_dist_compiled_react_cjs_react_development_js-a784779d\"), __webpack_require__.e(\"framework-node_modules_next_dist_compiled_r\"), __webpack_require__.e(\"framework-node_modules_next_dist_l\"), __webpack_require__.e(\"framework-node_modules_next_dist_shared_lib_a\"), __webpack_require__.e(\"framework-node_modules_next_dist_shared_lib_ha\"), __webpack_require__.e(\"framework-node_modules_next_dist_shared_lib_h\"), __webpack_require__.e(\"framework-node_modules_next_dist_shared_lib_lazy-dynamic_b\"), __webpack_require__.e(\"framework-node_modules_next_dist_shared_lib_m\"), __webpack_require__.e(\"framework-node_modules_next_dist_shared_lib_router-\"), __webpack_require__.e(\"framework-node_modules_next_dist_shared_lib_router_utils_o\"), __webpack_require__.e(\"framework-node_modules_next_dist_shared_lib_r\"), __webpack_require__.e(\"framework-node_modules_next_d\"), __webpack_require__.e(\"framework-node_modules_next_font_google_target_css-0\"), __webpack_require__.e(\"commons-i\"), __webpack_require__.e(\"commons-node_modules_framer-motion_dist_es_animation_animators_i\"), __webpack_require__.e(\"commons-node_modules_framer-motion_dist_es_a\"), __webpack_require__.e(\"commons-node_modules_framer-motion_dist_es_d\"), __webpack_require__.e(\"commons-node_modules_framer-motion_dist_es_motion_f\"), __webpack_require__.e(\"commons-node_modules_framer-motion_dist_es_projection_a\"), __webpack_require__.e(\"commons-node_modules_framer-motion_dist_es_projection_node_create-projection-node_mjs-d9cf742e\"), __webpack_require__.e(\"commons-node_modules_framer-motion_dist_es_render_VisualElement_mjs-19d9658a\"), __webpack_require__.e(\"commons-node_modules_framer-motion_dist_es_render_d\"), __webpack_require__.e(\"commons-node_modules_framer-motion_dist_es_r\"), __webpack_require__.e(\"commons-node_modules_framer-motion_dist_es_value_i\"), __webpack_require__.e(\"commons-node_modules_graphql-\"), __webpack_require__.e(\"commons-node_modules_graphql_language_a\"), __webpack_require__.e(\"commons-node_modules_graphql_language_parser_mjs-c45803c0\"), __webpack_require__.e(\"commons-node_modules_graphql_language_p\"), __webpack_require__.e(\"commons-node_modules_l\"), __webpack_require__.e(\"commons-node_modules_tailwind-merge_dist_bundle-mjs_mjs-a19ea93e\"), __webpack_require__.e(\"commons-node_modules_upstash_redis_chunk-5XANP4AV_mjs-ec81489a\"), __webpack_require__.e(\"commons-node_modules_zustand_esm_i\"), __webpack_require__.e(\"commons-src_components_c\"), __webpack_require__.e(\"commons-src_com\"), __webpack_require__.e(\"commons-src_lib_c\"), __webpack_require__.e(\"commons-src_lib_s\"), __webpack_require__.e(\"commons-src_lib_woocommerce_ts-ea0e4c9f\")]).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/woocommerce */ \"(app-pages-browser)/./src/lib/woocommerce.ts\"));\n                // Test basic connection\n                console.log(\"\\uD83D\\uDD17 Testing basic connection...\");\n                const connectionTest = await testWooCommerceConnection();\n                console.log(\"Connection test result:\", connectionTest);\n                // Test categories specifically\n                console.log(\"\\uD83D\\uDCC2 Testing categories...\");\n                const categories = await getAllCategories(20);\n                console.log(\"Categories result:\", categories);\n                // Test specific category queries\n                console.log(\"\\uD83D\\uDC54 Testing shirts category...\");\n                const shirtsCategory = await getCategoryProducts(\"shirts\", {\n                    first: 10\n                });\n                console.log(\"Shirts category result:\", shirtsCategory);\n                // Test alternative category names\n                const alternativeTests = [];\n                const alternativeNames = [\n                    \"shirt\",\n                    \"Shirts\",\n                    \"clothing\",\n                    \"apparel\"\n                ];\n                for (const name of alternativeNames){\n                    console.log(\"\\uD83D\\uDD0D Testing category: \".concat(name));\n                    try {\n                        var _result_products_nodes, _result_products, _result_products_nodes1, _result_products1;\n                        const result = await getCategoryProducts(name, {\n                            first: 5\n                        });\n                        alternativeTests.push({\n                            name,\n                            success: !!(result === null || result === void 0 ? void 0 : (_result_products = result.products) === null || _result_products === void 0 ? void 0 : (_result_products_nodes = _result_products.nodes) === null || _result_products_nodes === void 0 ? void 0 : _result_products_nodes.length),\n                            productCount: (result === null || result === void 0 ? void 0 : (_result_products1 = result.products) === null || _result_products1 === void 0 ? void 0 : (_result_products_nodes1 = _result_products1.nodes) === null || _result_products_nodes1 === void 0 ? void 0 : _result_products_nodes1.length) || 0,\n                            result: result\n                        });\n                    } catch (err) {\n                        alternativeTests.push({\n                            name,\n                            success: false,\n                            error: err instanceof Error ? err.message : \"Unknown error\"\n                        });\n                    }\n                }\n                setTestResults({\n                    connectionTest,\n                    categories: categories === null || categories === void 0 ? void 0 : categories.slice(0, 10),\n                    categoriesCount: (categories === null || categories === void 0 ? void 0 : categories.length) || 0,\n                    shirtsCategory,\n                    alternativeTests,\n                    timestamp: new Date().toISOString()\n                });\n            } catch (err) {\n                console.error(\"❌ Test failed:\", err);\n                setError(err instanceof Error ? err.message : \"Unknown error\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        runTests();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Testing WooCommerce connection...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-red-600 mb-6\",\n                        children: \"WooCommerce Test Failed\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold text-gray-900 mb-8\",\n                    children: \"WooCommerce Connection Test Results\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                            children: \"\\uD83D\\uDD17 Connection Test\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 rounded p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-sm overflow-auto\",\n                                children: JSON.stringify(testResults === null || testResults === void 0 ? void 0 : testResults.connectionTest, null, 2)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                            children: [\n                                \"\\uD83D\\uDCC2 Categories (\",\n                                (testResults === null || testResults === void 0 ? void 0 : testResults.categoriesCount) || 0,\n                                \" total)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                            children: testResults === null || testResults === void 0 ? void 0 : (_testResults_categories = testResults.categories) === null || _testResults_categories === void 0 ? void 0 : _testResults_categories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded p-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-gray-900\",\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                \"Slug: \",\n                                                category.slug\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                \"Count: \",\n                                                category.count || 0\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                \"ID: \",\n                                                category.id\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                            children: \"\\uD83D\\uDC54 Shirts Category Test\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 rounded p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-sm overflow-auto\",\n                                children: JSON.stringify(testResults === null || testResults === void 0 ? void 0 : testResults.shirtsCategory, null, 2)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                            children: \"\\uD83D\\uDD0D Alternative Category Tests\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: testResults === null || testResults === void 0 ? void 0 : (_testResults_alternativeTests = testResults.alternativeTests) === null || _testResults_alternativeTests === void 0 ? void 0 : _testResults_alternativeTests.map((test, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border rounded p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: [\n                                                        'Category: \"',\n                                                        test.name,\n                                                        '\"'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 rounded text-sm \".concat(test.success ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"),\n                                                    children: test.success ? \"✅ \".concat(test.productCount, \" products\") : \"❌ Failed\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this),\n                                        test.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-600 text-sm\",\n                                            children: test.error\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, this),\n                                        test.result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                            className: \"mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                    className: \"cursor-pointer text-sm text-gray-600\",\n                                                    children: \"View raw result\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"text-xs bg-gray-50 p-2 mt-2 rounded overflow-auto\",\n                                                    children: JSON.stringify(test.result, null, 2)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                            children: \"\\uD83D\\uDCCA Raw Test Results\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 rounded p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-xs overflow-auto max-h-96\",\n                                children: JSON.stringify(testResults, null, 2)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\test-woo\\\\page.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_s(TestWooCommercePage, \"hy6spxVwtkRc2Ezw2aCr0vJEdXs=\");\n_c = TestWooCommercePage;\nvar _c;\n$RefreshReg$(_c, \"TestWooCommercePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/test-woo/page.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["framework-node_modules_next_dist_a","framework-node_modules_next_dist_client_a","framework-node_modules_next_dist_client_components_ap","framework-node_modules_next_dist_client_components_b","framework-node_modules_next_dist_client_components_layout-router_js-4906aef6","framework-node_modules_next_dist_client_components_m","framework-node_modules_next_dist_client_components_p","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_C","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_LeftRightDi-d5fdd2e0","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_O","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_Overlay_mai-e776ae3b","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_Te","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_V","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_container_B","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_container_R","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_f","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_h","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_h","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_styles_B","framework-node_modules_next_dist_client_components_rea","framework-node_modules_next_dist_client_components_re","framework-node_modules_next_dist_client_components_router-reducer_co","framework-node_modules_next_dist_client_components_router-reducer_fe","framework-node_modules_next_dist_client_components_router-reducer_h","framework-node_modules_next_dist_client_components_router-reducer_pp","framework-node_modules_next_dist_client_components_router-reducer_reducers_f","framework-node_modules_next_dist_client_components_router-reducer_reducers_r","framework-node_modules_next_dist_client_components_router-reducer_r","framework-node_modules_next_dist_client_c","framework-node_modules_next_dist_client_g","framework-node_modules_next_dist_client_l","framework-node_modules_next_dist_compiled_a","framework-node_modules_next_dist_compiled_m","framework-node_modules_next_dist_compiled_react-dom_cjs_react-dom_development_js-3041f41d","framework-node_modules_next_dist_compiled_react-d","framework-node_modules_next_dist_compiled_react-server-dom-webpack_cjs_react-server-dom-webpack-clie-4912d8da","framework-node_modules_next_dist_compiled_react_cjs_react-jsx-dev-runtime_development_js-12999a20","framework-node_modules_next_dist_compiled_react_c","framework-node_modules_next_dist_compiled_react_cjs_react_development_js-a784779d","framework-node_modules_next_dist_compiled_r","framework-node_modules_next_dist_l","framework-node_modules_next_dist_shared_lib_a","framework-node_modules_next_dist_shared_lib_ha","framework-node_modules_next_dist_shared_lib_h","framework-node_modules_next_dist_shared_lib_lazy-dynamic_b","framework-node_modules_next_dist_shared_lib_m","framework-node_modules_next_dist_shared_lib_router-","framework-node_modules_next_dist_shared_lib_router_utils_o","framework-node_modules_next_dist_shared_lib_r","framework-node_modules_next_d","framework-node_modules_next_font_google_target_css-0","main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Capp%5C%5Ctest-woo%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);