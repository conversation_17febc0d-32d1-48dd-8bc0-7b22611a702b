"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-src_lib_s"],{

/***/ "(app-pages-browser)/./src/lib/store.ts":
/*!**************************!*\
  !*** ./src/lib/store.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCartStore: function() { return /* binding */ useCartStore; },\n/* harmony export */   useWishlistStore: function() { return /* binding */ useWishlistStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _woocommerce__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./woocommerce */ \"(app-pages-browser)/./src/lib/woocommerce.ts\");\n\n\n\n// Safe localStorage operation that won't cause errors during SSR\nconst safeLocalStorage = {\n    getItem: (name)=>{\n        if (false) {}\n        try {\n            return localStorage.getItem(name);\n        } catch (error) {\n            console.error(\"localStorage.getItem error:\", error);\n            return null;\n        }\n    },\n    setItem: (name, value)=>{\n        if (false) {}\n        try {\n            localStorage.setItem(name, value);\n        } catch (error) {\n            console.error(\"localStorage.setItem error:\", error);\n        }\n    },\n    removeItem: (name)=>{\n        if (false) {}\n        try {\n            localStorage.removeItem(name);\n        } catch (error) {\n            console.error(\"localStorage.removeItem error:\", error);\n        }\n    }\n};\n// Helper function to safely update cart state\nconst updateCartState = (set, normalizedCart)=>{\n    try {\n        if (!normalizedCart || !normalizedCart.lines) {\n            console.error(\"Invalid normalized cart data\", normalizedCart);\n            return;\n        }\n        const itemCount = normalizedCart.lines.reduce((acc, line)=>acc + (line.quantity || 0), 0);\n        const items = normalizedCart.lines.map((line)=>{\n            var _line_merchandise_product_image;\n            return {\n                id: line.id,\n                variantId: line.merchandise.id,\n                productId: line.merchandise.product.id,\n                title: line.merchandise.product.title,\n                handle: line.merchandise.product.handle,\n                image: ((_line_merchandise_product_image = line.merchandise.product.image) === null || _line_merchandise_product_image === void 0 ? void 0 : _line_merchandise_product_image.url) || \"\",\n                price: line.merchandise.price,\n                quantity: line.quantity,\n                currencyCode: line.merchandise.currencyCode\n            };\n        });\n        set({\n            items,\n            subtotal: normalizedCart.cost.subtotalAmount.amount,\n            total: normalizedCart.cost.totalAmount.amount,\n            currencyCode: normalizedCart.cost.totalAmount.currencyCode,\n            itemCount,\n            checkoutUrl: normalizedCart.checkoutUrl,\n            isLoading: false\n        });\n    } catch (error) {\n        console.error(\"Error updating cart state:\", error);\n        // Fallback to clearing state but keeping cart ID\n        set({\n            items: [],\n            subtotal: \"0.00\",\n            total: \"0.00\",\n            itemCount: 0,\n            isLoading: false\n        });\n    }\n};\nconst useCartStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        cartId: null,\n        items: [],\n        isOpen: false,\n        isLoading: false,\n        subtotal: \"0.00\",\n        total: \"0.00\",\n        currencyCode: \"USD\",\n        itemCount: 0,\n        checkoutUrl: null,\n        initializationInProgress: false,\n        initializationError: null,\n        openCart: ()=>set({\n                isOpen: true\n            }),\n        closeCart: ()=>set({\n                isOpen: false\n            }),\n        toggleCart: ()=>set((state)=>({\n                    isOpen: !state.isOpen\n                })),\n        initCart: async ()=>{\n            const state = get();\n            // Prevent multiple concurrent initialization\n            if (state.initializationInProgress) {\n                console.log(\"Cart initialization already in progress, skipping\");\n                return null;\n            }\n            set({\n                isLoading: true,\n                initializationInProgress: true,\n                initializationError: null\n            });\n            try {\n                // Check if we already have a cart ID\n                if (state.cartId) {\n                    // Validate the existing cart - note: getCart no longer needs cartId\n                    try {\n                        const existingCart = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCart)();\n                        if (existingCart) {\n                            set({\n                                isLoading: false,\n                                initializationInProgress: false\n                            });\n                            return existingCart;\n                        }\n                    } catch (error) {\n                        console.log(\"Existing cart validation failed, creating new cart\");\n                    // Fall through to create a new cart\n                    }\n                }\n                // Create a new cart\n                const newCart = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.createCart)();\n                if (newCart && newCart.id) {\n                    set({\n                        cartId: newCart.id,\n                        checkoutUrl: newCart.checkoutUrl,\n                        isLoading: false,\n                        initializationInProgress: false\n                    });\n                    console.log(\"Cart initialized with ID:\", newCart.id);\n                    return newCart;\n                }\n                throw new Error(\"Failed to create cart: No cart ID returned\");\n            } catch (error) {\n                console.error(\"Failed to initialize cart:\", error);\n                set({\n                    isLoading: false,\n                    initializationInProgress: false,\n                    initializationError: error instanceof Error ? error.message : \"Unknown error initializing cart\"\n                });\n                return null;\n            }\n        },\n        addItem: async (item)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                // Validate essential item properties\n                if (!item.variantId) {\n                    console.error(\"Cannot add item to cart: Missing variant ID\", item);\n                    set({\n                        isLoading: false\n                    });\n                    throw new Error(\"Missing variant ID for item\");\n                }\n                let cartId = get().cartId;\n                if (!cartId) {\n                    console.log(\"Cart not initialized, creating a new cart...\");\n                    const newCart = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.createCart)();\n                    if (newCart && newCart.id) {\n                        console.log(\"New cart created:\", newCart.id);\n                        cartId = newCart.id;\n                    } else {\n                        throw new Error(\"Failed to initialize cart\");\n                    }\n                }\n                // At this point cartId should be a valid string\n                if (!cartId) {\n                    throw new Error(\"Failed to initialize cart: No cart ID available\");\n                }\n                // Log the variant ID for debugging\n                console.log(\"Adding item to cart: \".concat(item.title, \" (\").concat(item.variantId, \"), quantity: \").concat(item.quantity));\n                try {\n                    const cart = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.addToCart)(cartId, [\n                        {\n                            merchandiseId: item.variantId,\n                            quantity: item.quantity || 1\n                        }\n                    ]);\n                    if (!cart) {\n                        throw new Error(\"Failed to add item to cart: No cart returned\");\n                    }\n                    // Normalize and update cart state\n                    const normalizedCart = (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.normalizeCart)(cart);\n                    updateCartState(set, normalizedCart);\n                    set({\n                        isOpen: true\n                    }); // Open cart when item is added\n                    console.log(\"Item added to cart successfully. Cart now has \".concat(normalizedCart.lines.length, \" items.\"));\n                } catch (apiError) {\n                    console.error(\"Shopify API error when adding to cart:\", apiError);\n                    // Re-throw with more context\n                    if (apiError instanceof Error) {\n                        throw new Error(\"Failed to add item to cart: \".concat(apiError.message));\n                    } else {\n                        throw new Error(\"Failed to add item to cart: Unknown API error\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Failed to add item to cart:\", error);\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        updateItem: async (id, quantity)=>{\n            const state = get();\n            set({\n                isLoading: true\n            });\n            try {\n                if (!state.cartId) {\n                    throw new Error(\"Cart not initialized\");\n                }\n                console.log(\"Updating item in cart: \".concat(id, \", new quantity: \").concat(quantity));\n                // If quantity is 0 or less, remove the item\n                if (quantity <= 0) {\n                    console.log(\"Quantity is \".concat(quantity, \", removing item from cart\"));\n                    return get().removeItem(id);\n                }\n                const cart = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.updateCart)(state.cartId, [\n                    {\n                        id,\n                        quantity\n                    }\n                ]);\n                if (!cart) {\n                    throw new Error(\"Failed to update item: No cart returned\");\n                }\n                // Normalize and update cart state\n                const normalizedCart = (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.normalizeCart)(cart);\n                updateCartState(set, normalizedCart);\n                console.log(\"Item updated successfully. Cart now has \".concat(normalizedCart.lines.length, \" items.\"));\n            } catch (error) {\n                console.error(\"Failed to update item in cart:\", error);\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        removeItem: async (id)=>{\n            const state = get();\n            set({\n                isLoading: true\n            });\n            try {\n                if (!state.cartId) {\n                    console.error(\"Cannot remove item: Cart not initialized\");\n                    throw new Error(\"Cart not initialized\");\n                }\n                console.log(\"Removing item from cart: \".concat(id));\n                // Get current cart state for comparison\n                const beforeItems = [\n                    ...state.items\n                ];\n                const itemBeingRemoved = beforeItems.find((item)=>item.id === id);\n                if (!itemBeingRemoved) {\n                    console.warn(\"Item with ID \".concat(id, \" not found in cart\"));\n                } else {\n                    console.log('Removing \"'.concat(itemBeingRemoved.title, '\" (').concat(itemBeingRemoved.variantId, \") from cart\"));\n                }\n                const cart = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.removeFromCart)(state.cartId, [\n                    id\n                ]);\n                if (!cart) {\n                    console.error(\"Failed to remove item: No cart returned from Shopify\");\n                    throw new Error(\"Failed to remove item: No cart returned\");\n                }\n                // Normalize and update cart state\n                const normalizedCart = (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.normalizeCart)(cart);\n                // Get updated items for comparison\n                const afterRemovalItems = normalizedCart.lines.map((line)=>({\n                        id: line.id,\n                        title: line.merchandise.product.title\n                    }));\n                console.log(\"Cart before removal:\", beforeItems.length, \"items\");\n                console.log(\"Cart after removal:\", afterRemovalItems.length, \"items\");\n                if (beforeItems.length === afterRemovalItems.length) {\n                    console.warn(\"Item count did not change after removal operation\");\n                }\n                updateCartState(set, normalizedCart);\n                console.log(\"Item removed successfully. Cart now has \".concat(normalizedCart.lines.length, \" items.\"));\n            } catch (error) {\n                console.error(\"Failed to remove item from cart:\", error);\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        clearCart: async ()=>{\n            const state = get();\n            set({\n                isLoading: true\n            });\n            try {\n                // When clearing the cart, we simply create a new empty cart in Shopify\n                // and update our local state to reflect that\n                console.log(\"Clearing cart and creating a new one\");\n                const cart = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.createCart)();\n                if (!cart) {\n                    throw new Error(\"Failed to create new cart\");\n                }\n                set({\n                    cartId: cart.id,\n                    items: [],\n                    subtotal: \"0.00\",\n                    total: \"0.00\",\n                    itemCount: 0,\n                    checkoutUrl: cart.checkoutUrl,\n                    isLoading: false\n                });\n                console.log(\"Cart cleared successfully. New cart ID:\", cart.id);\n            } catch (error) {\n                console.error(\"Failed to clear cart:\", error);\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        }\n    }), {\n    name: \"ankkor-cart\",\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.createJSONStorage)(()=>safeLocalStorage),\n    version: 1,\n    partialize: (state)=>({\n            cartId: state.cartId,\n            items: state.items,\n            subtotal: state.subtotal,\n            total: state.total,\n            currencyCode: state.currencyCode,\n            itemCount: state.itemCount,\n            checkoutUrl: state.checkoutUrl\n        })\n}));\nconst useWishlistStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        items: [],\n        isLoading: false,\n        addToWishlist: (item)=>{\n            set((state)=>{\n                // Check if item already exists in wishlist\n                if (state.items.some((wishlistItem)=>wishlistItem.id === item.id)) {\n                    return state; // Item already exists, don't add it again\n                }\n                return {\n                    items: [\n                        ...state.items,\n                        item\n                    ]\n                };\n            });\n        },\n        removeFromWishlist: (id)=>{\n            set((state)=>({\n                    items: state.items.filter((item)=>item.id !== id)\n                }));\n        },\n        clearWishlist: ()=>{\n            set({\n                items: []\n            });\n        },\n        isInWishlist: (id)=>{\n            return get().items.some((item)=>item.id === id);\n        }\n    }), {\n    name: \"ankkor-wishlist\",\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.createJSONStorage)(()=>safeLocalStorage),\n    partialize: (state)=>({\n            items: state.items\n        }),\n    skipHydration: true\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/store.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: function() { return /* binding */ cn; }\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Combines multiple class names into a single string, handling Tailwind CSS conflicts\n * @param inputs - Class names to be combined\n * @returns A merged class name string\n */ function cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFeEM7Ozs7Q0FJQyxHQUNNLFNBQVNFO0lBQUc7UUFBR0MsT0FBSCx1QkFBdUI7O0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG4vKipcbiAqIENvbWJpbmVzIG11bHRpcGxlIGNsYXNzIG5hbWVzIGludG8gYSBzaW5nbGUgc3RyaW5nLCBoYW5kbGluZyBUYWlsd2luZCBDU1MgY29uZmxpY3RzXG4gKiBAcGFyYW0gaW5wdXRzIC0gQ2xhc3MgbmFtZXMgdG8gYmUgY29tYmluZWRcbiAqIEByZXR1cm5zIEEgbWVyZ2VkIGNsYXNzIG5hbWUgc3RyaW5nXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/wooInventoryMapping.ts":
/*!****************************************!*\
  !*** ./src/lib/wooInventoryMapping.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addInventoryMapping: function() { return /* binding */ addInventoryMapping; },\n/* harmony export */   clearInventoryMappings: function() { return /* binding */ clearInventoryMappings; },\n/* harmony export */   getAllInventoryMappings: function() { return /* binding */ getAllInventoryMappings; },\n/* harmony export */   getAllShopifyToWooMappings: function() { return /* binding */ getAllShopifyToWooMappings; },\n/* harmony export */   getInventoryMapping: function() { return /* binding */ getInventoryMapping; },\n/* harmony export */   getProductSlugFromInventory: function() { return /* binding */ getProductSlugFromInventory; },\n/* harmony export */   getWooIdFromShopifyId: function() { return /* binding */ getWooIdFromShopifyId; },\n/* harmony export */   initializeFromProducts: function() { return /* binding */ initializeFromProducts; },\n/* harmony export */   loadInventoryMap: function() { return /* binding */ loadInventoryMap; },\n/* harmony export */   mapShopifyToWooId: function() { return /* binding */ mapShopifyToWooId; },\n/* harmony export */   saveInventoryMap: function() { return /* binding */ saveInventoryMap; },\n/* harmony export */   updateInventoryMapping: function() { return /* binding */ updateInventoryMapping; },\n/* harmony export */   updateInventoryMappings: function() { return /* binding */ updateInventoryMappings; },\n/* harmony export */   validateProductId: function() { return /* binding */ validateProductId; }\n/* harmony export */ });\n/* harmony import */ var _upstash_redis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @upstash/redis */ \"(app-pages-browser)/./node_modules/@upstash/redis/nodejs.mjs\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n// Redis key prefix for inventory mappings\nconst KEY_PREFIX = \"woo:inventory:mapping:\";\n// Redis key for the mapping between Shopify and WooCommerce IDs\nconst SHOPIFY_TO_WOO_KEY = \"shopify:to:woo:mapping\";\n// Initialize Redis client with support for both Upstash Redis and Vercel KV variables\nconst redis = new _upstash_redis__WEBPACK_IMPORTED_MODULE_0__.Redis({\n    url: process.env.UPSTASH_REDIS_REST_URL || process.env.NEXT_PUBLIC_KV_REST_API_URL || \"\",\n    token: process.env.UPSTASH_REDIS_REST_TOKEN || process.env.NEXT_PUBLIC_KV_REST_API_TOKEN || \"\"\n});\n// In-memory fallback for local development or when Redis is unavailable\nconst memoryStorage = {};\nconst shopifyToWooMemoryStorage = {};\n// Key for storing inventory mapping in KV store\nconst INVENTORY_MAPPING_KEY = \"woo-inventory-mapping\";\n/**\n * Check if Redis is available\n */ function isRedisAvailable() {\n    return Boolean(process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN || process.env.NEXT_PUBLIC_KV_REST_API_URL && process.env.NEXT_PUBLIC_KV_REST_API_TOKEN);\n}\n/**\n * Load inventory mapping from storage\n * Maps WooCommerce product IDs to product slugs\n * \n * @returns A record mapping product IDs to product slugs\n */ async function loadInventoryMap() {\n    // Use Redis if available\n    if (isRedisAvailable()) {\n        try {\n            // Get all keys with our prefix\n            const keys = await redis.keys(\"\".concat(KEY_PREFIX, \"*\"));\n            if (keys.length === 0) {\n                console.log(\"No existing WooCommerce inventory mappings found in Redis\");\n                return {};\n            }\n            // Create a mapping object\n            const map = {};\n            // Get all values in a single batch operation\n            const values = await redis.mget(...keys);\n            // Populate the mapping object\n            keys.forEach((key, index)=>{\n                const productId = key.replace(KEY_PREFIX, \"\");\n                const productSlug = values[index];\n                map[productId] = productSlug;\n            });\n            console.log(\"Loaded WooCommerce inventory mapping with \".concat(Object.keys(map).length, \" entries from Redis\"));\n            return map;\n        } catch (error) {\n            console.error(\"Error loading WooCommerce inventory mapping from Redis:\", error);\n            console.log(\"Falling back to in-memory storage\");\n            return {\n                ...memoryStorage\n            };\n        }\n    } else {\n        // Fallback to in-memory when Redis is not available\n        return {\n            ...memoryStorage\n        };\n    }\n}\n/**\n * Save inventory mapping to storage\n * \n * @param map The inventory mapping to save\n */ async function saveInventoryMap(map) {\n    // Use Redis if available\n    if (isRedisAvailable()) {\n        try {\n            // Convert map to array of Redis commands\n            const pipeline = redis.pipeline();\n            // First clear existing keys with this prefix\n            const existingKeys = await redis.keys(\"\".concat(KEY_PREFIX, \"*\"));\n            if (existingKeys.length > 0) {\n                pipeline.del(...existingKeys);\n            }\n            // Set new key-value pairs\n            Object.entries(map).forEach((param)=>{\n                let [productId, productSlug] = param;\n                pipeline.set(\"\".concat(KEY_PREFIX).concat(productId), productSlug);\n            });\n            // Execute all commands in a single transaction\n            await pipeline.exec();\n            console.log(\"Saved WooCommerce inventory mapping with \".concat(Object.keys(map).length, \" entries to Redis\"));\n        } catch (error) {\n            console.error(\"Error saving WooCommerce inventory mapping to Redis:\", error);\n            console.log(\"Falling back to in-memory storage\");\n            // Update in-memory storage as fallback\n            Object.assign(memoryStorage, map);\n        }\n    } else {\n        // Fallback to in-memory when Redis is not available\n        Object.assign(memoryStorage, map);\n        console.log(\"Saved WooCommerce inventory mapping with \".concat(Object.keys(map).length, \" entries to memory\"));\n    }\n}\n/**\n * Add a mapping between a WooCommerce product ID and a product slug\n * \n * @param productId The WooCommerce product ID\n * @param productSlug The product slug\n * @returns True if the mapping was added or updated, false if there was an error\n */ async function addInventoryMapping(productId, productSlug) {\n    try {\n        if (isRedisAvailable()) {\n            await redis.set(\"\".concat(KEY_PREFIX).concat(productId), productSlug);\n            console.log(\"Added WooCommerce mapping to Redis: \".concat(productId, \" -> \").concat(productSlug));\n        } else {\n            memoryStorage[productId] = productSlug;\n            console.log(\"Added WooCommerce mapping to memory: \".concat(productId, \" -> \").concat(productSlug));\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Error adding WooCommerce inventory mapping:\", error);\n        // Try memory as fallback\n        try {\n            memoryStorage[productId] = productSlug;\n            console.log(\"Added WooCommerce mapping to memory fallback: \".concat(productId, \" -> \").concat(productSlug));\n            return true;\n        } catch (memError) {\n            console.error(\"Error adding to memory fallback:\", memError);\n            return false;\n        }\n    }\n}\n/**\n * Get the product slug associated with a WooCommerce product ID\n * \n * @param productId The WooCommerce product ID\n * @returns The product slug, or null if not found\n */ async function getProductSlugFromInventory(productId) {\n    try {\n        if (isRedisAvailable()) {\n            const slug = await redis.get(\"\".concat(KEY_PREFIX).concat(productId));\n            return slug || null;\n        } else {\n            return memoryStorage[productId] || null;\n        }\n    } catch (error) {\n        console.error(\"Error getting product slug from Redis:\", error);\n        // Try memory as fallback\n        try {\n            return memoryStorage[productId] || null;\n        } catch (memError) {\n            console.error(\"Error getting from memory fallback:\", memError);\n            return null;\n        }\n    }\n}\n/**\n * Batch update multiple WooCommerce inventory mappings\n * \n * @param mappings An array of product ID to product slug mappings\n * @returns True if all mappings were successfully updated, false otherwise\n */ async function updateInventoryMappings(mappings) {\n    try {\n        if (isRedisAvailable()) {\n            const pipeline = redis.pipeline();\n            for (const { productId, productSlug } of mappings){\n                pipeline.set(\"\".concat(KEY_PREFIX).concat(productId), productSlug);\n            }\n            await pipeline.exec();\n            console.log(\"Updated \".concat(mappings.length, \" WooCommerce inventory mappings in Redis\"));\n        } else {\n            for (const { productId, productSlug } of mappings){\n                memoryStorage[productId] = productSlug;\n            }\n            console.log(\"Updated \".concat(mappings.length, \" WooCommerce inventory mappings in memory\"));\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Error batch updating WooCommerce inventory mappings:\", error);\n        return false;\n    }\n}\n/**\n * Get all WooCommerce inventory mappings\n * \n * @returns The complete inventory map\n */ async function getAllInventoryMappings() {\n    return await loadInventoryMap();\n}\n/**\n * Clear all WooCommerce inventory mappings\n * \n * @returns True if successfully cleared, false otherwise\n */ async function clearInventoryMappings() {\n    try {\n        if (isRedisAvailable()) {\n            const keys = await redis.keys(\"\".concat(KEY_PREFIX, \"*\"));\n            if (keys.length > 0) {\n                await redis.del(...keys);\n            }\n            console.log(\"Cleared all WooCommerce inventory mappings from Redis\");\n        }\n        // Clear memory storage regardless of Redis availability\n        Object.keys(memoryStorage).forEach((key)=>{\n            delete memoryStorage[key];\n        });\n        return true;\n    } catch (error) {\n        console.error(\"Error clearing WooCommerce inventory mappings:\", error);\n        return false;\n    }\n}\n/**\n * Map a Shopify product ID to a WooCommerce product ID\n * \n * @param shopifyId The Shopify product ID\n * @param wooId The WooCommerce product ID\n * @returns True if the mapping was added successfully, false otherwise\n */ async function mapShopifyToWooId(shopifyId, wooId) {\n    try {\n        if (isRedisAvailable()) {\n            // Get existing mappings\n            const existingMap = await redis.hgetall(SHOPIFY_TO_WOO_KEY) || {};\n            // Add new mapping\n            existingMap[shopifyId] = wooId;\n            // Save updated mappings\n            await redis.hset(SHOPIFY_TO_WOO_KEY, existingMap);\n            console.log(\"Mapped Shopify ID \".concat(shopifyId, \" to WooCommerce ID \").concat(wooId, \" in Redis\"));\n            return true;\n        } else {\n            // Fallback to in-memory\n            shopifyToWooMemoryStorage[shopifyId] = wooId;\n            console.log(\"Mapped Shopify ID \".concat(shopifyId, \" to WooCommerce ID \").concat(wooId, \" in memory\"));\n            return true;\n        }\n    } catch (error) {\n        console.error(\"Error mapping Shopify ID to WooCommerce ID:\", error);\n        return false;\n    }\n}\n/**\n * Get the WooCommerce ID corresponding to a Shopify ID\n * \n * @param shopifyId The original Shopify product ID or inventory item ID\n * @returns The corresponding WooCommerce ID, or null if not found\n */ async function getWooIdFromShopifyId(shopifyId) {\n    try {\n        if (isRedisAvailable()) {\n            const wooId = await redis.hget(SHOPIFY_TO_WOO_KEY, shopifyId);\n            return wooId || null;\n        } else {\n            return shopifyToWooMemoryStorage[shopifyId] || null;\n        }\n    } catch (error) {\n        console.error(\"Error getting WooCommerce ID for Shopify ID \".concat(shopifyId, \":\"), error);\n        // Try memory as fallback\n        try {\n            return shopifyToWooMemoryStorage[shopifyId] || null;\n        } catch (memError) {\n            console.error(\"Error getting from memory fallback:\", memError);\n            return null;\n        }\n    }\n}\n/**\n * Get all Shopify to WooCommerce ID mappings\n * \n * @returns Record of Shopify IDs to WooCommerce IDs\n */ async function getAllShopifyToWooMappings() {\n    try {\n        if (isRedisAvailable()) {\n            const mappings = await redis.hgetall(SHOPIFY_TO_WOO_KEY);\n            return mappings || {};\n        } else {\n            return {\n                ...shopifyToWooMemoryStorage\n            };\n        }\n    } catch (error) {\n        console.error(\"Error getting all Shopify to WooCommerce mappings:\", error);\n        return {\n            ...shopifyToWooMemoryStorage\n        };\n    }\n}\n/**\n * Initialize inventory mappings from WooCommerce products\n * This function should be called after initial product import or periodically to refresh the mappings\n * \n * @param products Array of WooCommerce products with id and slug properties\n * @returns True if successfully initialized, false otherwise\n */ async function initializeFromProducts(products) {\n    try {\n        const inventoryMappings = [];\n        const idMappings = [];\n        for (const product of products){\n            // Add to inventory mappings\n            inventoryMappings.push({\n                productId: product.id,\n                productSlug: product.slug\n            });\n            // If this product has a Shopify ID, add to ID mappings\n            if (product.shopifyId) {\n                idMappings.push({\n                    shopifyId: product.shopifyId,\n                    wooId: product.id\n                });\n            }\n        }\n        // Update inventory mappings\n        await updateInventoryMappings(inventoryMappings);\n        // Update ID mappings\n        for (const { shopifyId, wooId } of idMappings){\n            await mapShopifyToWooId(shopifyId, wooId);\n        }\n        console.log(\"Initialized \".concat(inventoryMappings.length, \" inventory mappings and \").concat(idMappings.length, \" ID mappings\"));\n        return true;\n    } catch (error) {\n        console.error(\"Error initializing inventory mappings from products:\", error);\n        return false;\n    }\n}\n/**\n * Get the current inventory mapping\n * \n * @returns The inventory mapping\n */ async function getInventoryMapping() {\n    try {\n        // Try to get the mapping from Redis\n        if (isRedisAvailable()) {\n            const allKeys = await redis.keys(\"\".concat(KEY_PREFIX, \"*\"));\n            if (allKeys.length > 0) {\n                const mapping = {};\n                const values = await redis.mget(...allKeys);\n                allKeys.forEach((key, index)=>{\n                    const productId = key.replace(KEY_PREFIX, \"\");\n                    const slug = values[index];\n                    mapping[productId] = {\n                        wooId: productId,\n                        inventory: 0,\n                        sku: \"\",\n                        title: slug,\n                        lastUpdated: new Date().toISOString()\n                    };\n                });\n                return mapping;\n            }\n        }\n        // Default empty mapping\n        return {};\n    } catch (error) {\n        console.error(\"Error getting inventory mapping:\", error);\n        return {};\n    }\n}\n/**\n * Update the inventory mapping\n * \n * @param mapping The inventory mapping to save\n * @returns True if successful, false otherwise\n */ async function updateInventoryMapping(mapping) {\n    try {\n        if (isRedisAvailable()) {\n            // First clear existing keys\n            const existingKeys = await redis.keys(\"\".concat(KEY_PREFIX, \"*\"));\n            if (existingKeys.length > 0) {\n                await redis.del(...existingKeys);\n            }\n            // Add each product mapping\n            const pipeline = redis.pipeline();\n            for (const [productId, details] of Object.entries(mapping)){\n                pipeline.set(\"\".concat(KEY_PREFIX).concat(productId), details.title || productId);\n            }\n            await pipeline.exec();\n            return true;\n        }\n        return false;\n    } catch (error) {\n        console.error(\"Error updating inventory mapping:\", error);\n        return false;\n    }\n}\n/**\n * Validate and transform product ID\n * \n * This function helps with the migration from Shopify to WooCommerce by:\n * 1. Checking if the ID is a valid WooCommerce ID\n * 2. If not, attempting to map from Shopify ID to WooCommerce ID\n * 3. Returning a normalized ID or the original ID if no mapping found\n * \n * @param productId The product ID to validate (could be Shopify or WooCommerce ID)\n * @returns A valid WooCommerce product ID or the original ID if no mapping found\n */ async function validateProductId(productId) {\n    if (!productId || productId === \"undefined\" || productId === \"null\") {\n        console.warn(\"Invalid product ID received:\", productId);\n        return productId; // Return the original ID even if invalid\n    }\n    // Check if this looks like a Shopify ID (gid://shopify/Product/123456789)\n    if (productId.includes(\"gid://shopify/Product/\")) {\n        console.log(\"Detected Shopify ID: \".concat(productId, \", attempting to map to WooCommerce ID\"));\n        // Try to get the WooCommerce ID from our mapping\n        const wooId = await getWooIdFromShopifyId(productId);\n        if (wooId) {\n            console.log(\"Mapped Shopify ID \".concat(productId, \" to WooCommerce ID \").concat(wooId));\n            return wooId;\n        } else {\n            console.warn(\"No mapping found for Shopify ID: \".concat(productId, \", using original ID\"));\n            return productId; // Return the original ID if no mapping found\n        }\n    }\n    // If it's a base64 encoded ID like \"cG9zdDo2MA==\", it might be a WooCommerce ID\n    // but we should check if it actually exists in our inventory mapping\n    if (productId.includes(\"=\")) {\n        const slug = await getProductSlugFromInventory(productId);\n        if (slug) {\n            // We have a mapping for this ID, so it's likely valid\n            return productId;\n        } else {\n            console.warn(\"Product ID \".concat(productId, \" not found in inventory mapping, using as is\"));\n            // We'll still return the ID and let the GraphQL API handle the validation\n            return productId;\n        }\n    }\n    // If it's a numeric ID, it's likely a valid WooCommerce product ID\n    if (/^\\d+$/.test(productId)) {\n        return productId;\n    }\n    // For any other format, return as is and let the GraphQL API validate\n    return productId;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/wooInventoryMapping.ts\n"));

/***/ })

}]);