"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-src_lib_c"],{

/***/ "(app-pages-browser)/./src/lib/currency.ts":
/*!*****************************!*\
  !*** ./src/lib/currency.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_CURRENCY_CODE: function() { return /* binding */ DEFAULT_CURRENCY_CODE; },\n/* harmony export */   DEFAULT_CURRENCY_SYMBOL: function() { return /* binding */ DEFAULT_CURRENCY_SYMBOL; },\n/* harmony export */   formatPrice: function() { return /* binding */ formatPrice; },\n/* harmony export */   formatPriceWithoutSymbol: function() { return /* binding */ formatPriceWithoutSymbol; },\n/* harmony export */   getCurrencySymbol: function() { return /* binding */ getCurrencySymbol; }\n/* harmony export */ });\n/**\r\n * Currency utility functions for Ankkor\r\n */ /**\r\n * Format a numeric price to a currency string\r\n */ function formatPrice(amount) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { locale = \"en-IN\", currency = \"INR\", minimumFractionDigits = 0, maximumFractionDigits = 2 } = options;\n    const numericAmount = typeof amount === \"string\" ? parseFloat(amount) : amount;\n    return new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency,\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(numericAmount);\n}\n/**\r\n * Get currency symbol for a given currency code\r\n */ function getCurrencySymbol() {\n    let currencyCode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"INR\", locale = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en-IN\";\n    return 0..toLocaleString(locale, {\n        style: \"currency\",\n        currency: currencyCode,\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n    }).replace(/\\d/g, \"\").trim();\n}\n/**\r\n * Format price without currency symbol\r\n */ function formatPriceWithoutSymbol(amount) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { locale = \"en-IN\", minimumFractionDigits = 0, maximumFractionDigits = 2 } = options;\n    const numericAmount = typeof amount === \"string\" ? parseFloat(amount) : amount;\n    return new Intl.NumberFormat(locale, {\n        style: \"decimal\",\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(numericAmount);\n}\n/**\r\n * Default currency symbol for the application\r\n */ const DEFAULT_CURRENCY_SYMBOL = \"₹\";\nconst DEFAULT_CURRENCY_CODE = \"INR\";\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/currency.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/eventBus.ts":
/*!*****************************!*\
  !*** ./src/lib/eventBus.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authEvents: function() { return /* binding */ authEvents; },\n/* harmony export */   cartEvents: function() { return /* binding */ cartEvents; },\n/* harmony export */   eventBus: function() { return /* binding */ eventBus; },\n/* harmony export */   notificationEvents: function() { return /* binding */ notificationEvents; },\n/* harmony export */   useEventBus: function() { return /* binding */ useEventBus; },\n/* harmony export */   useEventListener: function() { return /* binding */ useEventListener; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Type-safe event bus system for cross-component communication\n * Eliminates circular dependencies by providing event-driven architecture\n */ \n// Event bus class\nclass EventBus {\n    /**\n   * Subscribe to an event\n   */ on(event, listener) {\n        if (!this.listeners.has(event)) {\n            this.listeners.set(event, new Set());\n        }\n        this.listeners.get(event).add(listener);\n        // Return unsubscribe function\n        return ()=>{\n            this.off(event, listener);\n        };\n    }\n    /**\n   * Unsubscribe from an event\n   */ off(event, listener) {\n        const eventListeners = this.listeners.get(event);\n        if (eventListeners) {\n            eventListeners.delete(listener);\n            if (eventListeners.size === 0) {\n                this.listeners.delete(event);\n            }\n        }\n    }\n    /**\n   * Emit an event\n   */ emit(event, data) {\n        const eventListeners = this.listeners.get(event);\n        if (eventListeners) {\n            eventListeners.forEach((listener)=>{\n                try {\n                    listener(data);\n                } catch (error) {\n                    console.error(\"Error in event listener for \".concat(event, \":\"), error);\n                }\n            });\n        }\n    }\n    /**\n   * Subscribe to an event only once\n   */ once(event, listener) {\n        const onceListener = (data)=>{\n            listener(data);\n            this.off(event, onceListener);\n        };\n        this.on(event, onceListener);\n    }\n    /**\n   * Remove all listeners for an event or all events\n   */ removeAllListeners(event) {\n        if (event) {\n            this.listeners.delete(event);\n        } else {\n            this.listeners.clear();\n        }\n    }\n    /**\n   * Get the number of listeners for an event\n   */ listenerCount(event) {\n        var _this_listeners_get;\n        return ((_this_listeners_get = this.listeners.get(event)) === null || _this_listeners_get === void 0 ? void 0 : _this_listeners_get.size) || 0;\n    }\n    /**\n   * Get all event names that have listeners\n   */ eventNames() {\n        return Array.from(this.listeners.keys());\n    }\n    constructor(){\n        this.listeners = new Map();\n    }\n}\n// Create and export singleton instance\nconst eventBus = new EventBus();\n// Convenience hooks for React components\nconst useEventBus = ()=>eventBus;\n// Helper functions for common event patterns\nconst authEvents = {\n    loginSuccess: (user, token)=>eventBus.emit(\"auth:login-success\", {\n            user,\n            token\n        }),\n    loginError: (error)=>eventBus.emit(\"auth:login-error\", {\n            error\n        }),\n    logout: ()=>eventBus.emit(\"auth:logout\", undefined),\n    registerSuccess: (user, token)=>eventBus.emit(\"auth:register-success\", {\n            user,\n            token\n        }),\n    registerError: (error)=>eventBus.emit(\"auth:register-error\", {\n            error\n        }),\n    profileUpdated: (user)=>eventBus.emit(\"auth:profile-updated\", {\n            user\n        }),\n    sessionExpired: ()=>eventBus.emit(\"auth:session-expired\", undefined)\n};\nconst cartEvents = {\n    itemAdded: (item, message)=>eventBus.emit(\"cart:item-added\", {\n            item,\n            message\n        }),\n    itemRemoved: (itemId, message)=>eventBus.emit(\"cart:item-removed\", {\n            itemId,\n            message\n        }),\n    itemUpdated: (itemId, quantity, message)=>eventBus.emit(\"cart:item-updated\", {\n            itemId,\n            quantity,\n            message\n        }),\n    cleared: (message)=>eventBus.emit(\"cart:cleared\", {\n            message\n        }),\n    checkoutSuccess: (orderId, message)=>eventBus.emit(\"cart:checkout-success\", {\n            orderId,\n            message\n        }),\n    checkoutError: (error)=>eventBus.emit(\"cart:checkout-error\", {\n            error\n        }),\n    syncStarted: ()=>eventBus.emit(\"cart:sync-started\", undefined),\n    syncCompleted: ()=>eventBus.emit(\"cart:sync-completed\", undefined)\n};\nconst notificationEvents = {\n    show: function(message) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"info\", duration = arguments.length > 2 ? arguments[2] : void 0;\n        return eventBus.emit(\"notification:show\", {\n            message,\n            type,\n            duration\n        });\n    },\n    hide: (id)=>eventBus.emit(\"notification:hide\", {\n            id\n        })\n};\n// React hook for subscribing to events\nfunction useEventListener(event, listener) {\n    let deps = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const unsubscribe = eventBus.on(event, listener);\n        return unsubscribe;\n    }, deps);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/eventBus.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/localCartStore.ts":
/*!***********************************!*\
  !*** ./src/lib/localCartStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearCartAfterCheckout: function() { return /* binding */ clearCartAfterCheckout; },\n/* harmony export */   formatPrice: function() { return /* binding */ formatPrice; },\n/* harmony export */   useLocalCartCount: function() { return /* binding */ useLocalCartCount; },\n/* harmony export */   useLocalCartError: function() { return /* binding */ useLocalCartError; },\n/* harmony export */   useLocalCartItems: function() { return /* binding */ useLocalCartItems; },\n/* harmony export */   useLocalCartLoading: function() { return /* binding */ useLocalCartLoading; },\n/* harmony export */   useLocalCartStore: function() { return /* binding */ useLocalCartStore; },\n/* harmony export */   useLocalCartSubtotal: function() { return /* binding */ useLocalCartSubtotal; },\n/* harmony export */   useLocalCartTotal: function() { return /* binding */ useLocalCartTotal; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\n * Local Cart Store for Ankkor E-commerce\n *\n * This implementation uses local storage to persist cart data on the client side.\n * When the user proceeds to checkout, the cart items are sent to WooCommerce\n * using the Store API to create a server-side cart before redirecting to the checkout page.\n */ /* __next_internal_client_entry_do_not_use__ useLocalCartStore,useLocalCartItems,useLocalCartCount,useLocalCartSubtotal,useLocalCartTotal,useLocalCartLoading,useLocalCartError,formatPrice,clearCartAfterCheckout auto */ \n\n// Local storage version to handle migrations\nconst STORAGE_VERSION = 1;\n// Generate a unique ID for cart items\nconst generateItemId = ()=>{\n    return Math.random().toString(36).substring(2, 15);\n};\n// Validate product stock before adding to cart\nconst validateProductStock = async (productId, requestedQuantity, variationId)=>{\n    // Skip validation in development if API is not ready\n    if ( true && !process.env.ENABLE_STOCK_VALIDATION) {\n        console.log(\"Stock validation skipped in development mode\");\n        return {\n            available: true,\n            message: \"Development mode - validation skipped\"\n        };\n    }\n    try {\n        // Check real-time stock from your API\n        const response = await fetch(\"/api/products/\".concat(productId, \"/stock\").concat(variationId ? \"?variation_id=\".concat(variationId) : \"\"));\n        if (!response.ok) {\n            console.warn(\"Stock validation API failed, allowing add to cart\");\n            return {\n                available: true,\n                message: \"Stock validation temporarily unavailable\"\n            };\n        }\n        const stockData = await response.json();\n        // Check if product is in stock\n        if (stockData.stockStatus !== \"IN_STOCK\" && stockData.stockStatus !== \"instock\") {\n            return {\n                available: false,\n                message: \"This product is currently out of stock\",\n                stockStatus: stockData.stockStatus\n            };\n        }\n        // Check if requested quantity is available\n        if (stockData.stockQuantity !== null && stockData.stockQuantity < requestedQuantity) {\n            return {\n                available: false,\n                message: \"Only \".concat(stockData.stockQuantity, \" items available in stock\"),\n                stockQuantity: stockData.stockQuantity,\n                stockStatus: stockData.stockStatus\n            };\n        }\n        return {\n            available: true,\n            stockQuantity: stockData.stockQuantity,\n            stockStatus: stockData.stockStatus\n        };\n    } catch (error) {\n        console.error(\"Stock validation error:\", error);\n        // In case of error, allow the add to cart but log the issue\n        console.warn(\"Stock validation failed, allowing add to cart for better UX\");\n        return {\n            available: true,\n            message: \"Stock validation temporarily unavailable\"\n        };\n    }\n};\n// Create the store\nconst useLocalCartStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // State\n        items: [],\n        itemCount: 0,\n        isLoading: false,\n        error: null,\n        // Actions\n        addToCart: async (item)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                // Validate stock before adding to cart\n                const stockValidation = await validateProductStock(item.productId, item.quantity, item.variationId);\n                if (!stockValidation.available) {\n                    throw new Error(stockValidation.message || \"Product is out of stock\");\n                }\n                const items = get().items;\n                // Normalize price format - remove currency symbols and commas\n                let normalizedPrice = item.price;\n                if (typeof normalizedPrice === \"string\") {\n                    // Remove currency symbol if present\n                    const priceString = normalizedPrice.replace(/[₹$€£]/g, \"\").trim();\n                    // Replace comma with empty string if present (for Indian number format)\n                    normalizedPrice = priceString.replace(/,/g, \"\");\n                }\n                // Create a normalized item with clean price\n                const normalizedItem = {\n                    ...item,\n                    price: normalizedPrice\n                };\n                // Check if the item already exists in the cart\n                const existingItemIndex = items.findIndex((cartItem)=>cartItem.productId === normalizedItem.productId && cartItem.variationId === normalizedItem.variationId);\n                if (existingItemIndex !== -1) {\n                    // If item exists, update quantity\n                    const updatedItems = [\n                        ...items\n                    ];\n                    updatedItems[existingItemIndex].quantity += normalizedItem.quantity;\n                    set({\n                        items: updatedItems,\n                        itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                        isLoading: false\n                    });\n                } else {\n                    // If item doesn't exist, add it with a new ID\n                    const newItem = {\n                        ...normalizedItem,\n                        id: generateItemId()\n                    };\n                    set({\n                        items: [\n                            ...items,\n                            newItem\n                        ],\n                        itemCount: items.reduce((sum, item)=>sum + item.quantity, 0) + newItem.quantity,\n                        isLoading: false\n                    });\n                }\n                // Show success message\n                console.log(\"Item added to cart successfully\");\n                // Store the updated cart in localStorage immediately to prevent loss\n                if (true) {\n                    try {\n                        const state = {\n                            state: {\n                                items: get().items,\n                                itemCount: get().itemCount,\n                                isLoading: false,\n                                error: null\n                            },\n                            version: STORAGE_VERSION\n                        };\n                        localStorage.setItem(\"ankkor-local-cart\", JSON.stringify(state));\n                    } catch (storageError) {\n                        console.warn(\"Failed to manually persist cart to localStorage:\", storageError);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error adding item to cart:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n            }\n        },\n        updateCartItem: (id, quantity)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const items = get().items;\n                if (quantity <= 0) {\n                    // If quantity is 0 or negative, remove the item\n                    return get().removeCartItem(id);\n                }\n                // Find the item and update its quantity\n                const updatedItems = items.map((item)=>item.id === id ? {\n                        ...item,\n                        quantity\n                    } : item);\n                set({\n                    items: updatedItems,\n                    itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                    isLoading: false\n                });\n                // Immediately persist to localStorage\n                if (true) {\n                    try {\n                        const state = {\n                            state: {\n                                items: updatedItems,\n                                itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                                isLoading: false,\n                                error: null\n                            },\n                            version: STORAGE_VERSION\n                        };\n                        localStorage.setItem(\"ankkor-local-cart\", JSON.stringify(state));\n                    } catch (storageError) {\n                        console.warn(\"Failed to manually persist cart update to localStorage:\", storageError);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error updating cart item:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n            }\n        },\n        removeCartItem: (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const items = get().items;\n                const updatedItems = items.filter((item)=>item.id !== id);\n                set({\n                    items: updatedItems,\n                    itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                    isLoading: false\n                });\n                // Immediately persist to localStorage\n                if (true) {\n                    try {\n                        const state = {\n                            state: {\n                                items: updatedItems,\n                                itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                                isLoading: false,\n                                error: null\n                            },\n                            version: STORAGE_VERSION\n                        };\n                        localStorage.setItem(\"ankkor-local-cart\", JSON.stringify(state));\n                    } catch (storageError) {\n                        console.warn(\"Failed to manually persist cart removal to localStorage:\", storageError);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error removing cart item:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n            }\n        },\n        clearCart: ()=>{\n            set({\n                items: [],\n                itemCount: 0,\n                isLoading: false,\n                error: null\n            });\n            // Immediately persist to localStorage\n            if (true) {\n                try {\n                    const state = {\n                        state: {\n                            items: [],\n                            itemCount: 0,\n                            isLoading: false,\n                            error: null\n                        },\n                        version: STORAGE_VERSION\n                    };\n                    localStorage.setItem(\"ankkor-local-cart\", JSON.stringify(state));\n                } catch (storageError) {\n                    console.warn(\"Failed to manually persist cart clearing to localStorage:\", storageError);\n                }\n            }\n        },\n        setError: (error)=>{\n            set({\n                error\n            });\n        },\n        setIsLoading: (isLoading)=>{\n            set({\n                isLoading\n            });\n        },\n        // Helper methods\n        subtotal: ()=>{\n            const items = get().items;\n            try {\n                const calculatedSubtotal = items.reduce((total, item)=>{\n                    // Handle price with or without currency symbol\n                    let itemPrice = 0;\n                    if (typeof item.price === \"string\") {\n                        // Remove currency symbol if present\n                        const priceString = item.price.replace(/[₹$€£]/g, \"\").trim();\n                        // Replace comma with empty string if present (for Indian number format)\n                        const cleanPrice = priceString.replace(/,/g, \"\");\n                        itemPrice = parseFloat(cleanPrice);\n                    } else {\n                        itemPrice = item.price;\n                    }\n                    if (isNaN(itemPrice)) {\n                        console.warn(\"Invalid price for item \".concat(item.id, \": \").concat(item.price));\n                        return total;\n                    }\n                    return total + itemPrice * item.quantity;\n                }, 0);\n                return isNaN(calculatedSubtotal) ? 0 : calculatedSubtotal;\n            } catch (error) {\n                console.error(\"Error calculating subtotal:\", error);\n                return 0;\n            }\n        },\n        total: ()=>{\n            // For now, total is the same as subtotal\n            // In the future, you could add shipping, tax, etc.\n            const calculatedTotal = get().subtotal();\n            return isNaN(calculatedTotal) ? 0 : calculatedTotal;\n        },\n        // Sync cart with WooCommerce using Store API\n        syncWithWooCommerce: async (authToken)=>{\n            const { items } = get();\n            if (items.length === 0) {\n                throw new Error(\"Cart is empty\");\n            }\n            try {\n                console.log(\"Syncing cart with WooCommerce...\");\n                console.log(\"Auth token provided:\", !!authToken);\n                set({\n                    isLoading: true\n                });\n                // If user is logged in, use the JWT-to-Cookie bridge for seamless checkout\n                if (authToken) {\n                    console.log(\"User is authenticated, using JWT-to-Cookie bridge\");\n                    try {\n                        const checkoutUrl = await createWpSessionAndGetCheckoutUrl(authToken, items);\n                        set({\n                            isLoading: false\n                        });\n                        return checkoutUrl;\n                    } catch (bridgeError) {\n                        console.error(\"JWT-to-Cookie bridge failed:\", bridgeError);\n                        // Fall back to guest checkout if the bridge fails\n                        console.log(\"Falling back to guest checkout...\");\n                    // Continue with guest checkout flow below\n                    }\n                }\n                // For guest users, redirect directly to WooCommerce checkout\n                console.log(\"User is not authenticated, redirecting to WooCommerce checkout\");\n                const baseUrl = \"https://maroon-lapwing-781450.hostingersite.com\" || 0;\n                const checkoutUrl = \"\".concat(baseUrl, \"/checkout/\");\n                console.log(\"Guest checkout URL:\", checkoutUrl);\n                set({\n                    isLoading: false\n                });\n                return checkoutUrl;\n            } catch (error) {\n                console.error(\"Error syncing cart with WooCommerce:\", error);\n                set({\n                    isLoading: false\n                });\n                // Fallback approach: use URL parameters to build cart\n                try {\n                    console.log(\"Attempting fallback method for cart sync...\");\n                    const baseUrl = \"https://maroon-lapwing-781450.hostingersite.com\" || 0;\n                    // Build URL with add-to-cart parameters for each item\n                    let checkoutUrl = \"\".concat(baseUrl, \"/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1\");\n                    // Add each item as a URL parameter\n                    items.forEach((item, index)=>{\n                        if (index === 0) {\n                            checkoutUrl += \"&add-to-cart=\".concat(item.productId, \"&quantity=\").concat(item.quantity);\n                        } else {\n                            // For WooCommerce, additional items need a different format\n                            checkoutUrl += \"&add-to-cart[\".concat(index, \"]=\").concat(item.productId, \"&quantity[\").concat(index, \"]=\").concat(item.quantity);\n                        }\n                        // Add variation ID if present\n                        if (item.variationId) {\n                            checkoutUrl += \"&variation_id=\".concat(item.variationId);\n                        }\n                    });\n                    console.log(\"Fallback checkout URL:\", checkoutUrl);\n                    return checkoutUrl;\n                } catch (fallbackError) {\n                    console.error(\"Fallback method failed:\", fallbackError);\n                    throw new Error(\"Failed to sync cart with WooCommerce. Please try again or contact support.\");\n                }\n            }\n        }\n    }), {\n    name: \"ankkor-local-cart\",\n    version: STORAGE_VERSION,\n    skipHydration: true\n}));\n// Helper hooks\nconst useLocalCartItems = ()=>useLocalCartStore((state)=>state.items);\nconst useLocalCartCount = ()=>useLocalCartStore((state)=>state.itemCount);\nconst useLocalCartSubtotal = ()=>useLocalCartStore((state)=>state.subtotal());\nconst useLocalCartTotal = ()=>useLocalCartStore((state)=>state.total());\nconst useLocalCartLoading = ()=>useLocalCartStore((state)=>state.isLoading);\nconst useLocalCartError = ()=>useLocalCartStore((state)=>state.error);\n// Helper functions\nconst formatPrice = function(price) {\n    let currencyCode = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"INR\";\n    const amount = typeof price === \"string\" ? parseFloat(price) : price;\n    return new Intl.NumberFormat(\"en-IN\", {\n        style: \"currency\",\n        currency: currencyCode,\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(amount);\n};\n// Clear cart after successful checkout\nconst clearCartAfterCheckout = ()=>{\n    useLocalCartStore.getState().clearCart();\n// Also reset the cart token to ensure a fresh cart for the next session\n// cartSession.resetCartToken(); // This line was removed as per the edit hint\n};\n/**\n * Create WordPress session from JWT token and get the checkout URL\n * This implements the JWT-to-Cookie Bridge for seamless checkout experience\n * @param authToken The JWT authentication token\n * @param items Cart items to include in checkout\n * @returns The WooCommerce checkout URL\n */ async function createWpSessionAndGetCheckoutUrl(authToken, items) {\n    if (!authToken) {\n        throw new Error(\"Authentication token is required\");\n    }\n    const wpUrl = \"https://maroon-lapwing-781450.hostingersite.com\";\n    const checkoutUrl = \"https://maroon-lapwing-781450.hostingersite.com/checkout/\";\n    if (!wpUrl || !checkoutUrl) {\n        throw new Error(\"WordPress or checkout URL not configured. Check your environment variables.\");\n    }\n    try {\n        console.log(\"Creating WordPress session from JWT token...\");\n        console.log(\"Using endpoint:\", \"\".concat(wpUrl, \"/wp-json/headless/v1/create-wp-session\"));\n        console.log(\"Token length:\", authToken.length);\n        console.log(\"Token preview:\", authToken.substring(0, 20) + \"...\");\n        // Call the custom WordPress endpoint to create a session from JWT\n        const response = await fetch(\"\".concat(wpUrl, \"/wp-json/headless/v1/create-wp-session\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(authToken)\n            },\n            // THIS IS THE CRITICAL LINE - Include token in request body as well\n            body: JSON.stringify({\n                token: authToken\n            }),\n            credentials: \"include\"\n        });\n        console.log(\"Response status:\", response.status);\n        console.log(\"Response headers:\", Object.fromEntries(response.headers.entries()));\n        if (!response.ok) {\n            let errorMessage = \"HTTP \".concat(response.status, \": \").concat(response.statusText);\n            try {\n                const errorData = await response.json();\n                errorMessage = errorData.message || errorData.code || errorMessage;\n                console.error(\"Error response data:\", errorData);\n            } catch (parseError) {\n                console.error(\"Could not parse error response:\", parseError);\n            }\n            throw new Error(\"Failed to create WordPress session: \".concat(errorMessage));\n        }\n        const data = await response.json();\n        console.log(\"Response data:\", data);\n        if (!data.success) {\n            throw new Error(data.message || \"Failed to create WordPress session\");\n        }\n        console.log(\"WordPress session created successfully\");\n        console.log(\"Redirecting to checkout URL:\", checkoutUrl);\n        // For authenticated users, we can directly go to checkout\n        // The server already has the user's session and will load the correct cart\n        return checkoutUrl;\n    } catch (error) {\n        console.error(\"Error creating WordPress session:\", error);\n        // Provide more specific error messages\n        if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n            throw new Error(\"Network error: Could not connect to WordPress. Please check your internet connection.\");\n        }\n        throw new Error(error instanceof Error ? error.message : \"Failed to prepare checkout\");\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/localCartStore.ts\n"));

/***/ })

}]);